/**
 * Error Recovery Manager for Bulk Processing
 * Handles partial failures, data validation, and retry mechanisms
 */

export interface RecoveryContext {
  jobId: string;
  operationType: 'tool_creation' | 'ai_generation' | 'media_collection' | 'status_update';
  partialData?: any;
  failurePoint: string;
  error: string;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

export interface RecoveryResult {
  success: boolean;
  recovered: boolean;
  partialSuccess: boolean;
  savedData?: any;
  error?: string;
  nextAction?: 'retry' | 'manual_review' | 'skip' | 'abort';
}

export interface ValidationError {
  field: string;
  value: any;
  error: string;
  severity: 'error' | 'warning';
}

export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * Error Recovery Manager
 * Provides comprehensive error recovery and partial failure handling
 */
export class ErrorRecoveryManager {
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
    retryableErrors: [
      'network_timeout',
      'rate_limit_exceeded',
      'temporary_unavailable',
      'connection_error',
      'api_timeout'
    ]
  };

  /**
   * Attempt to recover from a partial failure
   */
  async recoverFromFailure(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`🔄 Attempting recovery for job ${context.jobId} at ${context.failurePoint}`);

    try {
      // Validate recovery context
      const validationResult = this.validateRecoveryContext(context);
      if (!validationResult.isValid) {
        return {
          success: false,
          recovered: false,
          partialSuccess: false,
          error: `Invalid recovery context: ${validationResult.errors.join(', ')}`,
          nextAction: 'abort'
        };
      }

      // Check if error is retryable
      if (!this.isRetryableError(context.error)) {
        return await this.handleNonRetryableError(context);
      }

      // Check retry limits
      if (context.retryCount >= context.maxRetries) {
        return await this.handleMaxRetriesExceeded(context);
      }

      // Attempt recovery based on operation type
      switch (context.operationType) {
        case 'tool_creation':
          return await this.recoverToolCreation(context);
        case 'ai_generation':
          return await this.recoverAiGeneration(context);
        case 'media_collection':
          return await this.recoverMediaCollection(context);
        case 'status_update':
          return await this.recoverStatusUpdate(context);
        default:
          return {
            success: false,
            recovered: false,
            partialSuccess: false,
            error: `Unknown operation type: ${context.operationType}`,
            nextAction: 'manual_review'
          };
      }

    } catch (error) {
      console.error(`Recovery attempt failed for job ${context.jobId}:`, error);
      return {
        success: false,
        recovered: false,
        partialSuccess: false,
        error: (error as Error).message,
        nextAction: 'manual_review'
      };
    }
  }

  /**
   * Validate data with comprehensive error reporting
   */
  validateData(data: any, schema: any): {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationError[];
    sanitizedData?: any;
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const sanitizedData: any = {};

    // Validate required fields
    if (schema.required) {
      for (const field of schema.required) {
        if (!data[field]) {
          errors.push({
            field,
            value: data[field],
            error: `Required field '${field}' is missing`,
            severity: 'error'
          });
        }
      }
    }

    // Validate field types and constraints
    for (const [field, fieldSchema] of Object.entries(schema.properties || {})) {
      const value = data[field];
      const fieldConfig = fieldSchema as any;

      if (value !== undefined && value !== null) {
        // Type validation
        if (fieldConfig.type && typeof value !== fieldConfig.type) {
          errors.push({
            field,
            value,
            error: `Expected type '${fieldConfig.type}', got '${typeof value}'`,
            severity: 'error'
          });
          continue;
        }

        // String length validation
        if (fieldConfig.type === 'string' && typeof value === 'string') {
          if (fieldConfig.minLength && value.length < fieldConfig.minLength) {
            errors.push({
              field,
              value,
              error: `Minimum length is ${fieldConfig.minLength}, got ${value.length}`,
              severity: 'error'
            });
          }
          if (fieldConfig.maxLength && value.length > fieldConfig.maxLength) {
            if (fieldConfig.truncate) {
              sanitizedData[field] = value.substring(0, fieldConfig.maxLength);
              warnings.push({
                field,
                value,
                error: `Value truncated to ${fieldConfig.maxLength} characters`,
                severity: 'warning'
              });
            } else {
              errors.push({
                field,
                value,
                error: `Maximum length is ${fieldConfig.maxLength}, got ${value.length}`,
                severity: 'error'
              });
            }
          } else {
            sanitizedData[field] = value;
          }
        } else {
          sanitizedData[field] = value;
        }

        // URL validation
        if (fieldConfig.format === 'url' && typeof value === 'string') {
          try {
            new URL(value);
            sanitizedData[field] = value;
          } catch {
            errors.push({
              field,
              value,
              error: 'Invalid URL format',
              severity: 'error'
            });
          }
        }

        // Email validation
        if (fieldConfig.format === 'email' && typeof value === 'string') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            errors.push({
              field,
              value,
              error: 'Invalid email format',
              severity: 'error'
            });
          } else {
            sanitizedData[field] = value;
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitizedData: errors.length === 0 ? sanitizedData : undefined
    };
  }

  /**
   * Retry operation with exponential backoff
   */
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    let lastError: Error;

    for (let attempt = 0; attempt < retryConfig.maxRetries; attempt++) {
      try {
        const result = await operation();
        if (attempt > 0) {
          console.log(`✅ Operation succeeded on attempt ${attempt + 1}`);
        }
        return result;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retryConfig.maxRetries - 1) {
          break; // Last attempt, don't wait
        }

        if (!this.isRetryableError(lastError.message)) {
          throw lastError; // Don't retry non-retryable errors
        }

        const delay = Math.min(
          retryConfig.baseDelayMs * Math.pow(retryConfig.backoffMultiplier, attempt),
          retryConfig.maxDelayMs
        );

        console.log(`⏳ Attempt ${attempt + 1} failed, retrying in ${delay}ms: ${lastError.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Save partial progress for later recovery
   */
  async savePartialProgress(
    jobId: string,
    operationType: string,
    partialData: any,
    failurePoint: string
  ): Promise<void> {
    const recoveryData = {
      jobId,
      operationType,
      partialData,
      failurePoint,
      timestamp: new Date().toISOString(),
      status: 'partial_failure'
    };

    // In a real implementation, this would save to a recovery table
    console.log(`💾 Saved partial progress for job ${jobId}:`, recoveryData);
  }

  /**
   * Check if an error is retryable
   */
  public isRetryableError(error: string): boolean {
    const errorLower = error.toLowerCase();
    return this.defaultRetryConfig.retryableErrors.some(retryableError =>
      errorLower.includes(retryableError)
    );
  }

  /**
   * Validate recovery context
   */
  private validateRecoveryContext(context: RecoveryContext): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!context.jobId) {
      errors.push('Job ID is required');
    }

    if (!context.operationType) {
      errors.push('Operation type is required');
    }

    if (!context.failurePoint) {
      errors.push('Failure point is required');
    }

    if (!context.error) {
      errors.push('Error message is required');
    }

    if (context.retryCount < 0) {
      errors.push('Retry count cannot be negative');
    }

    if (context.maxRetries < 0) {
      errors.push('Max retries cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Recover from tool creation failure
   */
  private async recoverToolCreation(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`🔧 Recovering tool creation for job ${context.jobId}`);

    // Check if partial tool data exists
    if (context.partialData?.toolId) {
      // Tool was created but other steps failed
      return {
        success: true,
        recovered: true,
        partialSuccess: true,
        savedData: { toolId: context.partialData.toolId },
        nextAction: 'retry'
      };
    }

    // No partial data, need to retry from beginning
    return {
      success: false,
      recovered: false,
      partialSuccess: false,
      nextAction: 'retry'
    };
  }

  /**
   * Recover from AI generation failure
   */
  private async recoverAiGeneration(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`🤖 Recovering AI generation for job ${context.jobId}`);

    // Check if partial AI content exists
    if (context.partialData?.generatedContent) {
      await this.savePartialProgress(
        context.jobId,
        'ai_generation',
        context.partialData.generatedContent,
        context.failurePoint
      );

      return {
        success: true,
        recovered: true,
        partialSuccess: true,
        savedData: context.partialData.generatedContent,
        nextAction: 'retry'
      };
    }

    return {
      success: false,
      recovered: false,
      partialSuccess: false,
      nextAction: 'retry'
    };
  }

  /**
   * Recover from media collection failure
   */
  private async recoverMediaCollection(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`📸 Recovering media collection for job ${context.jobId}`);

    // Check if partial media exists
    if (context.partialData?.mediaAssets) {
      await this.savePartialProgress(
        context.jobId,
        'media_collection',
        context.partialData.mediaAssets,
        context.failurePoint
      );

      return {
        success: true,
        recovered: true,
        partialSuccess: true,
        savedData: context.partialData.mediaAssets,
        nextAction: 'retry'
      };
    }

    return {
      success: false,
      recovered: false,
      partialSuccess: false,
      nextAction: 'retry'
    };
  }

  /**
   * Recover from status update failure
   */
  private async recoverStatusUpdate(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`📊 Recovering status update for job ${context.jobId}`);

    // Status updates are usually safe to retry
    return {
      success: false,
      recovered: false,
      partialSuccess: false,
      nextAction: 'retry'
    };
  }

  /**
   * Handle non-retryable errors
   */
  private async handleNonRetryableError(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`❌ Non-retryable error for job ${context.jobId}: ${context.error}`);

    // Save any partial data for manual review
    if (context.partialData) {
      await this.savePartialProgress(
        context.jobId,
        context.operationType,
        context.partialData,
        context.failurePoint
      );
    }

    return {
      success: false,
      recovered: false,
      partialSuccess: !!context.partialData,
      savedData: context.partialData,
      error: context.error,
      nextAction: 'manual_review'
    };
  }

  /**
   * Handle max retries exceeded
   */
  private async handleMaxRetriesExceeded(context: RecoveryContext): Promise<RecoveryResult> {
    console.log(`⚠️ Max retries exceeded for job ${context.jobId}`);

    // Save any partial data for manual review
    if (context.partialData) {
      await this.savePartialProgress(
        context.jobId,
        context.operationType,
        context.partialData,
        context.failurePoint
      );
    }

    return {
      success: false,
      recovered: false,
      partialSuccess: !!context.partialData,
      savedData: context.partialData,
      error: `Max retries (${context.maxRetries}) exceeded`,
      nextAction: 'manual_review'
    };
  }
}

// Singleton instance
let errorRecoveryManagerInstance: ErrorRecoveryManager | null = null;

export function getErrorRecoveryManager(): ErrorRecoveryManager {
  if (!errorRecoveryManagerInstance) {
    errorRecoveryManagerInstance = new ErrorRecoveryManager();
  }
  return errorRecoveryManagerInstance;
}
