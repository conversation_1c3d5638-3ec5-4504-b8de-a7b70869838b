/**
 * Bulk Processing Job Handler
 * Handles bulk processing jobs within the job queue system
 */

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '../types';
import { BulkProcessingJobData } from '../types';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';
import { getJobMemoryManager } from '../memory-manager';

export class BulkProcessingHandler implements JobHandler {
  private memoryManager = getJobMemoryManager();
  async handle(job: Job): Promise<any> {
    try {
      console.log(`🔄 Processing bulk job ${job.id} with ${job.data.urls.length} URLs`);

      const bulkEngine = getBulkProcessingEngine();

      // Register job resources
      this.memoryManager.registerResource(job.id, {
        id: `bulk_engine_${job.id}`,
        type: 'child_job',
        data: { jobId: job.id, type: 'bulk_processing' },
        createdAt: new Date(),
      });

      // Convert URLs to BulkToolData format
      const bulkToolData = (job.data as BulkProcessingJobData).urls.map((url: string) => ({
        url,
        providedData: {},
        needsGeneration: {
          name: true,
          description: true,
          features: true,
          pricing: true,
          prosAndCons: true,
          haiku: true,
          hashtags: true,
        },
      }));

      // Create bulk processing job
      const jobData = job.data as BulkProcessingJobData;
      const bulkJob = await bulkEngine.createBulkJob(
        bulkToolData,
        jobData.options,
        jobData.metadata
      );

      // Register the bulk job as a resource
      this.memoryManager.registerResource(job.id, {
        id: `bulk_job_${bulkJob.id}`,
        type: 'child_job',
        data: { jobId: bulkJob.id, type: 'bulk_job' },
        createdAt: new Date(),
      });

      console.log(`✅ Bulk job ${job.id} created successfully as ${bulkJob.id}`);

      return {
        success: true,
        bulkJobId: bulkJob.id,
        totalItems: bulkJob.totalItems,
        message: `Bulk processing job created with ${bulkJob.totalItems} items`,
      };
    } catch (error) {
      console.error(`❌ Bulk processing job ${job.id} failed:`, error);
      // Cleanup resources on error
      await this.cleanup(job);
      throw error;
    }
  }

  async cleanup(job: Job): Promise<void> {
    console.log(`🧹 Cleaning up bulk processing job ${job.id}`);

    try {
      // Cleanup all resources associated with this job
      await this.memoryManager.cleanupJobResources(job.id);
    } catch (error) {
      console.error(`Failed to cleanup bulk processing job ${job.id}:`, error);
    }
  }
}
