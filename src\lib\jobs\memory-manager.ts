/**
 * Memory Manager for Job System
 * Handles resource cleanup and memory monitoring for job handlers
 */

export interface JobResource {
  id: string;
  type: 'temp_file' | 'cache_entry' | 'child_job' | 'network_connection' | 'timer';
  data: any;
  createdAt: Date;
  jobId: string;
}

export interface MemoryStats {
  heapUsed: number;
  heapTotal: number;
  external: number;
  activeResources: number;
  activeJobs: number;
}

/**
 * Memory Manager for Job System
 * Tracks and cleans up resources associated with jobs
 */
export class JobMemoryManager {
  private resources = new Map<string, JobResource>();
  private jobResources = new Map<string, Set<string>>();
  private memoryMonitor: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startMemoryMonitoring();
    this.startPeriodicCleanup();
  }

  /**
   * Register a resource for a job
   */
  registerResource(jobId: string, resource: Omit<JobResource, 'jobId'>): string {
    const resourceId = `${resource.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullResource: JobResource = {
      ...resource,
      id: resourceId,
      jobId,
    };

    this.resources.set(resourceId, fullResource);
    
    if (!this.jobResources.has(jobId)) {
      this.jobResources.set(jobId, new Set());
    }
    this.jobResources.get(jobId)!.add(resourceId);

    console.log(`📝 Registered resource ${resourceId} for job ${jobId}`);
    return resourceId;
  }

  /**
   * Unregister a specific resource
   */
  unregisterResource(resourceId: string): void {
    const resource = this.resources.get(resourceId);
    if (!resource) return;

    // Remove from job resources
    const jobResources = this.jobResources.get(resource.jobId);
    if (jobResources) {
      jobResources.delete(resourceId);
      if (jobResources.size === 0) {
        this.jobResources.delete(resource.jobId);
      }
    }

    // Cleanup the resource
    this.cleanupResource(resource);
    this.resources.delete(resourceId);

    console.log(`🗑️ Unregistered resource ${resourceId}`);
  }

  /**
   * Cleanup all resources for a job
   */
  async cleanupJobResources(jobId: string): Promise<void> {
    const resourceIds = this.jobResources.get(jobId);
    if (!resourceIds) return;

    console.log(`🧹 Cleaning up ${resourceIds.size} resources for job ${jobId}`);

    const cleanupPromises = Array.from(resourceIds).map(async (resourceId) => {
      try {
        const resource = this.resources.get(resourceId);
        if (resource) {
          await this.cleanupResource(resource);
          this.resources.delete(resourceId);
        }
      } catch (error) {
        console.error(`Failed to cleanup resource ${resourceId}:`, error);
      }
    });

    await Promise.allSettled(cleanupPromises);
    this.jobResources.delete(jobId);

    console.log(`✅ Cleaned up all resources for job ${jobId}`);
  }

  /**
   * Cleanup a specific resource
   */
  private async cleanupResource(resource: JobResource): Promise<void> {
    try {
      switch (resource.type) {
        case 'temp_file':
          // Cleanup temporary files
          if (resource.data.path) {
            console.log(`🗑️ Cleaning up temp file: ${resource.data.path}`);
            // In a real implementation, you would delete the file
            // await fs.unlink(resource.data.path);
          }
          break;

        case 'cache_entry':
          // Cleanup cache entries
          if (resource.data.key) {
            console.log(`🗑️ Cleaning up cache entry: ${resource.data.key}`);
            // In a real implementation, you would clear the cache
            // await cache.delete(resource.data.key);
          }
          break;

        case 'child_job':
          // Cleanup child jobs
          if (resource.data.jobId) {
            console.log(`🗑️ Cleaning up child job: ${resource.data.jobId}`);
            // In a real implementation, you would cancel/cleanup the job
            // await jobManager.deleteJob(resource.data.jobId);
          }
          break;

        case 'network_connection':
          // Cleanup network connections
          if (resource.data.connection) {
            console.log(`🗑️ Cleaning up network connection: ${resource.id}`);
            // In a real implementation, you would close the connection
            // resource.data.connection.close();
          }
          break;

        case 'timer':
          // Cleanup timers
          if (resource.data.timerId) {
            console.log(`🗑️ Cleaning up timer: ${resource.id}`);
            clearTimeout(resource.data.timerId);
          }
          break;

        default:
          console.log(`⚠️ Unknown resource type: ${resource.type}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup resource ${resource.id}:`, error);
    }
  }

  /**
   * Get memory statistics
   */
  getMemoryStats(): MemoryStats {
    const memUsage = process.memoryUsage();
    
    return {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      activeResources: this.resources.size,
      activeJobs: this.jobResources.size,
    };
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitor = setInterval(() => {
      const stats = this.getMemoryStats();
      console.log(`📊 Memory: ${stats.heapUsed}MB/${stats.heapTotal}MB, Resources: ${stats.activeResources}, Jobs: ${stats.activeJobs}`);
      
      // Trigger cleanup if memory usage is high
      if (stats.heapUsed > 500 || stats.activeResources > 1000) {
        console.log('⚠️ High memory usage detected, triggering cleanup');
        this.performEmergencyCleanup();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Start periodic cleanup
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performPeriodicCleanup();
    }, 300000); // Cleanup every 5 minutes
  }

  /**
   * Perform periodic cleanup of stale resources
   */
  private async performPeriodicCleanup(): Promise<void> {
    console.log('🧹 Performing periodic resource cleanup...');
    
    const now = Date.now();
    const staleResources: string[] = [];
    
    // Find stale resources (older than 1 hour)
    for (const [resourceId, resource] of this.resources.entries()) {
      const resourceAge = now - resource.createdAt.getTime();
      const oneHour = 60 * 60 * 1000;
      
      if (resourceAge > oneHour) {
        staleResources.push(resourceId);
      }
    }
    
    // Cleanup stale resources
    for (const resourceId of staleResources) {
      this.unregisterResource(resourceId);
    }
    
    console.log(`✅ Periodic cleanup completed (${staleResources.length} stale resources cleaned)`);
  }

  /**
   * Perform emergency cleanup when memory is high
   */
  private async performEmergencyCleanup(): Promise<void> {
    console.log('🚨 Performing emergency resource cleanup...');
    
    const now = Date.now();
    const oldResources: string[] = [];
    
    // Find resources older than 30 minutes
    for (const [resourceId, resource] of this.resources.entries()) {
      const resourceAge = now - resource.createdAt.getTime();
      const thirtyMinutes = 30 * 60 * 1000;
      
      if (resourceAge > thirtyMinutes) {
        oldResources.push(resourceId);
      }
    }
    
    // Cleanup old resources
    for (const resourceId of oldResources) {
      this.unregisterResource(resourceId);
    }
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    console.log(`🚨 Emergency cleanup completed (${oldResources.length} resources cleaned)`);
  }

  /**
   * Shutdown the memory manager
   */
  shutdown(): void {
    console.log('🛑 Shutting down memory manager...');
    
    // Clear intervals
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
      this.memoryMonitor = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Cleanup all resources
    const allResourceIds = Array.from(this.resources.keys());
    for (const resourceId of allResourceIds) {
      this.unregisterResource(resourceId);
    }
    
    console.log('✅ Memory manager shutdown complete');
  }
}

// Singleton instance
let memoryManagerInstance: JobMemoryManager | null = null;

export function getJobMemoryManager(): JobMemoryManager {
  if (!memoryManagerInstance) {
    memoryManagerInstance = new JobMemoryManager();
  }
  return memoryManagerInstance;
}
