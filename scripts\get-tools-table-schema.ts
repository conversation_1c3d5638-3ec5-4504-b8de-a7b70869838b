#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getToolsTableSchema() {
  console.log('🔍 Getting complete tools table schema from Supabase...\n');

  try {
    // First, let's get a sample record to see what columns exist
    const { data: sampleTool, error: sampleError } = await supabase
      .from('tools')
      .select('*')
      .limit(1);

    if (sampleError) {
      console.error('❌ Error fetching sample tool:', sampleError);
      return;
    }

    let columns: string[] = [];
    if (sampleTool && sampleTool.length > 0) {
      columns = Object.keys(sampleTool[0]);
    } else {
      // If no data, try to insert a test record to see the schema
      console.log('📊 No existing tools found, checking schema via insert...');

      const { error: insertError } = await supabase
        .from('tools')
        .insert({
          id: 'schema-test-' + Date.now(),
          name: 'Schema Test Tool',
          slug: 'schema-test-tool-' + Date.now(),
          link: '/tools/schema-test'
        });

      if (insertError) {
        console.log('Insert error details:', insertError);
        // Extract column names from error message if possible
        if (insertError.message.includes('column')) {
          console.log('❌ Schema check via insert failed, but we can see some column info in error');
        }
      } else {
        // Clean up test record
        await supabase
          .from('tools')
          .delete()
          .eq('id', 'schema-test-' + Date.now());
      }
    }

    console.log('📋 COMPLETE TOOLS TABLE SCHEMA:');
    console.log('=' .repeat(80));

    if (columns && columns.length > 0) {
      columns.forEach((col: string, index: number) => {
        console.log(`${(index + 1).toString().padStart(2, ' ')}. ${col}`);
      });

      console.log('=' .repeat(80));
      console.log(`\n📊 Total columns: ${columns.length}`);

      // Group columns by category
      const columnNames = columns;
      
      console.log('\n🏷️  COLUMN CATEGORIES:');
      
      // Core fields
      const coreFields = columnNames.filter((name: string) => 
        ['id', 'name', 'slug', 'description', 'short_description', 'detailed_description', 'link', 'website'].includes(name)
      );
      console.log(`\n   Core Fields (${coreFields.length}):`, coreFields.join(', '));
      
      // Content fields
      const contentFields = columnNames.filter((name: string) => 
        ['features', 'pricing', 'pricing_type', 'pricing_details', 'pros', 'cons', 'faqs', 'use_cases', 'target_audience', 'integrations'].includes(name)
      );
      console.log(`\n   Content Fields (${contentFields.length}):`, contentFields.join(', '));
      
      // SEO fields
      const seoFields = columnNames.filter((name: string) => 
        ['meta_title', 'meta_description', 'meta_keywords'].includes(name)
      );
      console.log(`\n   SEO Fields (${seoFields.length}):`, seoFields.join(', '));
      
      // Media fields
      const mediaFields = columnNames.filter((name: string) => 
        ['logo_url', 'screenshots', 'primary_image', 'primary_image_type', 'media_source', 'media_updated_at'].includes(name)
      );
      console.log(`\n   Media Fields (${mediaFields.length}):`, mediaFields.join(', '));
      
      // Workflow fields
      const workflowFields = columnNames.filter((name: string) => 
        ['content_status', 'submission_type', 'submission_source', 'submission_id', 'submitted_by', 'submission_date', 'approved_by', 'approved_at', 'editorial_notes'].includes(name)
      );
      console.log(`\n   Workflow Fields (${workflowFields.length}):`, workflowFields.join(', '));
      
      // AI fields
      const aiFields = columnNames.filter((name: string) => 
        ['scraped_data', 'ai_generation_status', 'ai_generation_job_id', 'last_scraped_at', 'last_ai_update', 'content_quality_score', 'generated_content'].includes(name)
      );
      console.log(`\n   AI Fields (${aiFields.length}):`, aiFields.join(', '));
      
      // Categorization fields
      const categoryFields = columnNames.filter((name: string) => 
        ['category_id', 'subcategory', 'tags', 'company'].includes(name)
      );
      console.log(`\n   Category Fields (${categoryFields.length}):`, categoryFields.join(', '));
      
      // Status fields
      const statusFields = columnNames.filter((name: string) => 
        ['is_verified', 'is_claimed'].includes(name)
      );
      console.log(`\n   Status Fields (${statusFields.length}):`, statusFields.join(', '));
      
      // Timestamp fields
      const timestampFields = columnNames.filter((name: string) => 
        ['created_at', 'updated_at', 'published_at'].includes(name)
      );
      console.log(`\n   Timestamp Fields (${timestampFields.length}):`, timestampFields.join(', '));
      
      // Legacy JSONB fields
      const legacyFields = columnNames.filter((name: string) => 
        ['social_links', 'pros_and_cons', 'haiku', 'hashtags', 'releases', 'claim_info'].includes(name)
      );
      console.log(`\n   Legacy JSONB Fields (${legacyFields.length}):`, legacyFields.join(', '));
      
      // Versioning fields
      const versionFields = columnNames.filter((name: string) => 
        ['current_version_id', 'version_count', 'editorial_review_id'].includes(name)
      );
      console.log(`\n   Versioning Fields (${versionFields.length}):`, versionFields.join(', '));
      
      // Other fields
      const otherFields = columnNames.filter((name: string) => 
        ![...coreFields, ...contentFields, ...seoFields, ...mediaFields, ...workflowFields, ...aiFields, ...categoryFields, ...statusFields, ...timestampFields, ...legacyFields, ...versionFields].includes(name)
      );
      if (otherFields.length > 0) {
        console.log(`\n   Other Fields (${otherFields.length}):`, otherFields.join(', '));
      }
      
    } else {
      console.log('❌ No columns found for tools table');
    }

  } catch (error) {
    console.error('❌ Schema fetch failed:', error);
  }
}

// Run the audit
getToolsTableSchema().catch(console.error);
