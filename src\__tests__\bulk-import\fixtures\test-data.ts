/**
 * Test Fixtures for Bulk Import Testing
 * Comprehensive test data for CSV/JSON import scenarios
 */

import { describe, test, expect } from '@jest/globals';

// Dummy test to prevent <PERSON><PERSON> from failing on fixture files
describe('Test Data Fixtures', () => {
  test('should export test data correctly', () => {
    expect(validTestTools).toBeDefined();
    expect(importTestScenarios).toBeDefined();
  });
});

export interface TestTool {
  name: string;
  link: string;
  description?: string;
  short_description?: string;
  detailed_description?: string;
  website?: string;
  category_id?: string;
  subcategory?: string;
  company?: string;
  is_verified?: boolean;
  is_claimed?: boolean;
  content_status?: string;
  features?: any;
  pricing?: any;
  logo_url?: string;
}

export interface ImportTestScenario {
  name: string;
  description: string;
  data: TestTool[];
  expectedResults: {
    imported: number;
    skipped: number;
    errors: number;
    duplicates: number;
  };
  duplicateStrategy?: 'skip' | 'update' | 'error';
}

// Valid test tools for successful import scenarios
export const validTestTools: TestTool[] = [
  {
    name: 'AI Writing Assistant',
    link: '/tools/ai-writing-assistant',
    description: 'Advanced AI-powered writing tool',
    short_description: 'AI writing helper',
    detailed_description: 'Comprehensive AI writing assistant with grammar checking and style suggestions',
    website: 'https://aiwriter.example.com',
    category_id: 'ai-tools',
    subcategory: 'writing',
    company: 'AI Writer Inc',
    is_verified: true,
    is_claimed: false,
    content_status: 'published',
    features: { grammar_check: true, style_suggestions: true },
    pricing: { type: 'freemium', starting_price: 9.99 }
  },
  {
    name: 'Code Generator Pro',
    link: '/tools/code-generator-pro',
    description: 'Professional code generation tool',
    short_description: 'Code generator',
    detailed_description: 'Generate high-quality code in multiple programming languages',
    website: 'https://codegen.example.com',
    category_id: 'ai-tools',
    subcategory: 'development',
    company: 'CodeGen Corp',
    is_verified: false,
    is_claimed: true,
    content_status: 'draft',
    features: { multi_language: true, templates: true },
    pricing: { type: 'subscription', monthly_price: 29.99 }
  },
  {
    name: 'Image Enhancer AI',
    link: '/tools/image-enhancer-ai',
    description: 'AI-powered image enhancement',
    short_description: 'Image enhancer',
    detailed_description: 'Enhance and upscale images using advanced AI algorithms',
    website: 'https://imageai.example.com',
    category_id: 'ai-tools',
    subcategory: 'image-processing',
    company: 'ImageAI Ltd',
    is_verified: true,
    is_claimed: true,
    content_status: 'published',
    features: { upscaling: true, noise_reduction: true },
    pricing: { type: 'pay_per_use', cost_per_image: 0.10 }
  }
];

// Invalid test tools for error handling scenarios
export const invalidTestTools: TestTool[] = [
  {
    name: '', // Missing required name
    link: '/tools/invalid-tool-1',
    description: 'Tool with missing name'
  },
  {
    name: 'Tool Without Link',
    link: '', // Missing required link
    description: 'Tool with missing link'
  },
  {
    name: 'Tool With Invalid JSON',
    link: '/tools/invalid-json-tool',
    description: 'Tool with malformed JSON features',
    features: 'invalid-json-string' as any
  }
];

// Duplicate test tools for duplicate detection scenarios
export const duplicateTestTools: TestTool[] = [
  {
    name: 'AI Writing Assistant', // Duplicate name from validTestTools
    link: '/tools/duplicate-ai-writer',
    description: 'Another AI writing tool'
  },
  {
    name: 'Duplicate Code Tool',
    link: '/tools/code-generator-pro', // Duplicate link (slug) from validTestTools
    description: 'Tool with duplicate slug'
  }
];

// Test scenarios for comprehensive testing
export const importTestScenarios: ImportTestScenario[] = [
  {
    name: 'Valid Import - All Success',
    description: 'Import valid tools with no duplicates or errors',
    data: validTestTools,
    expectedResults: {
      imported: 3,
      skipped: 0,
      errors: 0,
      duplicates: 0
    },
    duplicateStrategy: 'skip'
  },
  {
    name: 'Invalid Data - Validation Errors',
    description: 'Import tools with validation errors',
    data: invalidTestTools,
    expectedResults: {
      imported: 0,
      skipped: 0,
      errors: 3,
      duplicates: 0
    },
    duplicateStrategy: 'skip'
  },
  {
    name: 'Duplicate Detection - Skip Strategy',
    description: 'Import tools with duplicates using skip strategy',
    data: [...validTestTools, ...duplicateTestTools],
    expectedResults: {
      imported: 3,
      skipped: 2,
      errors: 0,
      duplicates: 2
    },
    duplicateStrategy: 'skip'
  },
  {
    name: 'Duplicate Detection - Error Strategy',
    description: 'Import tools with duplicates using error strategy',
    data: [...validTestTools, ...duplicateTestTools],
    expectedResults: {
      imported: 3,
      skipped: 0,
      errors: 2,
      duplicates: 2
    },
    duplicateStrategy: 'error'
  },
  {
    name: 'Mixed Scenario - Valid, Invalid, and Duplicates',
    description: 'Comprehensive test with all types of data',
    data: [...validTestTools, ...invalidTestTools, ...duplicateTestTools],
    expectedResults: {
      imported: 3,
      skipped: 2,
      errors: 5, // 3 invalid + 2 duplicates with error strategy
      duplicates: 2
    },
    duplicateStrategy: 'error'
  }
];

// CSV test data
export const csvTestData = {
  validCSV: `Name,Link,Description,Category Id,Company,Is Verified
AI Writing Assistant,/tools/ai-writing-assistant,Advanced AI-powered writing tool,ai-tools,AI Writer Inc,true
Code Generator Pro,/tools/code-generator-pro,Professional code generation tool,ai-tools,CodeGen Corp,false
Image Enhancer AI,/tools/image-enhancer-ai,AI-powered image enhancement,ai-tools,ImageAI Ltd,true`,

  invalidCSV: `Name,Link,Description
,/tools/invalid-1,Missing name
Tool Without Link,,Missing link
Valid Tool,/tools/valid,Valid description`,

  malformedCSV: `Name,Link,Description
"Unclosed quote tool,/tools/unclosed,Description
Normal Tool,/tools/normal,Normal description`,

  emptyCSV: `Name,Link,Description`,

  headerOnlyCSV: `Name,Link,Description,Category Id`
};

// JSON test data
export const jsonTestData = {
  validJSONArray: validTestTools,
  
  validJSONWithToolsProperty: {
    tools: validTestTools
  },
  
  validJSONWithDataProperty: {
    data: validTestTools
  },
  
  invalidJSONStructure: {
    items: validTestTools // Wrong property name
  },
  
  invalidJSONSyntax: '{"tools": [{"name": "Invalid JSON"', // Malformed JSON
  
  emptyJSONArray: [],
  
  emptyJSONObject: {}
};

// File size test data
export const fileSizeTestData = {
  // Generate large CSV data for size testing
  generateLargeCSV: (rows: number): string => {
    const header = 'Name,Link,Description,Category Id\n';
    const dataRows = Array.from({ length: rows }, (_, i) => 
      `Tool ${i + 1},/tools/tool-${i + 1},Description for tool ${i + 1},ai-tools`
    ).join('\n');
    return header + dataRows;
  },
  
  // Generate large JSON data for size testing
  generateLargeJSON: (items: number): TestTool[] => {
    return Array.from({ length: items }, (_, i) => ({
      name: `Tool ${i + 1}`,
      link: `/tools/tool-${i + 1}`,
      description: `Description for tool ${i + 1}`,
      category_id: 'ai-tools'
    }));
  }
};

// Transaction test data for rollback scenarios
export const transactionTestData = {
  partialFailureScenario: [
    ...validTestTools.slice(0, 2), // First 2 valid tools
    {
      name: 'Tool That Will Fail',
      link: '/tools/failure-tool',
      description: 'This tool will cause a database error'
    },
    ...validTestTools.slice(2) // Remaining valid tools
  ]
};
