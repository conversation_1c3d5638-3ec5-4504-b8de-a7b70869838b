/**
 * Test Helper Utilities for Bulk Import Testing
 * Utilities for creating test files, mocking APIs, and validating results
 */

import { describe, test, expect } from '@jest/globals';
import { TestTool, ImportTestScenario } from '../fixtures/test-data';

// Dummy test to prevent <PERSON><PERSON> from failing on utility files
describe('Test Helper Utilities', () => {
  test('should export helper functions correctly', () => {
    expect(createMockFile).toBeDefined();
    expect(createCSVContent).toBeDefined();
    expect(createJSONContent).toBeDefined();
  });
});

export interface MockFile {
  name: string;
  type: string;
  size: number;
  text: () => Promise<string>;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: Array<{
    row: number;
    error: string;
    data: any;
  }>;
  duplicates: Array<{
    row: number;
    name: string;
    slug: string;
  }>;
}

/**
 * Create a mock File object for testing
 */
export function createMockFile(content: string, filename: string, type: string): MockFile {
  const blob = new Blob([content], { type });
  return {
    name: filename,
    type: type,
    size: blob.size,
    text: async () => content
  } as MockFile;
}

/**
 * Create CSV content from test tool data
 */
export function createCSVContent(tools: TestTool[]): string {
  if (tools.length === 0) {
    return 'Name,Link,Description,Category Id';
  }

  // Create header from first tool's keys
  const headers = Object.keys(tools[0]);
  const headerRow = headers.map(header => 
    header.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  ).join(',');

  // Create data rows
  const dataRows = tools.map(tool => {
    return headers.map(header => {
      const value = tool[header as keyof TestTool];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return '';
      } else if (typeof value === 'object') {
        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      } else if (typeof value === 'string' && value.includes(',')) {
        return `"${value.replace(/"/g, '""')}"`;
      } else {
        return String(value);
      }
    }).join(',');
  });

  return [headerRow, ...dataRows].join('\n');
}

/**
 * Create JSON content from test tool data
 */
export function createJSONContent(tools: TestTool[], format: 'array' | 'tools' | 'data' = 'array'): string {
  switch (format) {
    case 'tools':
      return JSON.stringify({ tools }, null, 2);
    case 'data':
      return JSON.stringify({ data: tools }, null, 2);
    default:
      return JSON.stringify(tools, null, 2);
  }
}

/**
 * Create FormData for file upload testing
 */
export function createFormData(
  file: MockFile, 
  options: {
    preview?: boolean;
    duplicateStrategy?: string;
  } = {}
): FormData {
  const formData = new FormData();
  formData.append('file', file as any);
  
  if (options.preview) {
    formData.append('preview', 'true');
  }
  
  if (options.duplicateStrategy) {
    formData.append('duplicateStrategy', options.duplicateStrategy);
  }
  
  return formData;
}

/**
 * Validate import result against expected results
 */
export function validateImportResult(
  actual: ImportResult, 
  expected: ImportTestScenario['expectedResults']
): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (actual.imported !== expected.imported) {
    errors.push(`Expected ${expected.imported} imported, got ${actual.imported}`);
  }

  if (actual.skipped !== expected.skipped) {
    errors.push(`Expected ${expected.skipped} skipped, got ${actual.skipped}`);
  }

  if (actual.errors.length !== expected.errors) {
    errors.push(`Expected ${expected.errors} errors, got ${actual.errors.length}`);
  }

  if (actual.duplicates.length !== expected.duplicates) {
    errors.push(`Expected ${expected.duplicates} duplicates, got ${actual.duplicates.length}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Generate test file with specific size for size limit testing
 */
export function generateTestFileWithSize(targetSizeBytes: number, format: 'csv' | 'json'): MockFile {
  let content = '';
  const baseContent = format === 'csv' 
    ? 'Name,Link,Description\nTest Tool,/tools/test,Test description\n'
    : '{"tools":[{"name":"Test Tool","link":"/tools/test","description":"Test description"}]}';
  
  // Repeat content until we reach target size
  while (content.length < targetSizeBytes) {
    content += baseContent;
  }
  
  // Trim to exact size if needed
  if (content.length > targetSizeBytes) {
    content = content.substring(0, targetSizeBytes);
  }
  
  const filename = format === 'csv' ? 'large-test.csv' : 'large-test.json';
  const mimeType = format === 'csv' ? 'text/csv' : 'application/json';
  
  return createMockFile(content, filename, mimeType);
}

/**
 * Create malformed file content for error testing
 */
export function createMalformedContent(type: 'csv' | 'json', errorType: string): string {
  switch (type) {
    case 'csv':
      switch (errorType) {
        case 'unclosed-quotes':
          return 'Name,Link,Description\n"Unclosed quote tool,/tools/test,Description';
        case 'mismatched-columns':
          return 'Name,Link,Description\nTool 1,/tools/1\nTool 2,/tools/2,Description,Extra Column';
        case 'empty-header':
          return ',Link,Description\nTool 1,/tools/1,Description';
        default:
          return 'Invalid CSV Content';
      }
    case 'json':
      switch (errorType) {
        case 'syntax-error':
          return '{"tools": [{"name": "Test"'; // Missing closing brackets
        case 'invalid-structure':
          return '{"invalid": "structure"}';
        case 'non-array':
          return '"not an object or array"';
        default:
          return 'Invalid JSON Content';
      }
    default:
      return 'Invalid Content';
  }
}

/**
 * Mock database responses for testing
 */
export const mockDatabaseResponses = {
  // Mock successful tool creation
  createToolSuccess: {
    data: { id: 'mock-tool-id' },
    error: null
  },
  
  // Mock tool creation failure
  createToolFailure: {
    data: null,
    error: new Error('Database constraint violation')
  },
  
  // Mock existing tools for duplicate detection
  existingTools: [
    {
      id: 'existing-1',
      name: 'AI Writing Assistant',
      slug: 'ai-writing-assistant'
    },
    {
      id: 'existing-2', 
      name: 'Code Generator Pro',
      slug: 'code-generator-pro'
    }
  ],
  
  // Mock empty tools list
  emptyToolsList: []
};

/**
 * Create test request object for API testing
 */
export function createTestRequest(
  url: string,
  method: string = 'POST',
  body?: FormData | string
): Request {
  const init: RequestInit = {
    method,
    headers: {
      'x-api-key': 'test-admin-key'
    }
  };
  
  if (body) {
    init.body = body;
  }
  
  return new Request(url, init);
}

/**
 * Simulate file upload progress for testing
 */
export function simulateUploadProgress(
  onProgress: (progress: number) => void,
  duration: number = 1000
): Promise<void> {
  return new Promise((resolve) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      onProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        resolve();
      }
    }, duration / 10);
  });
}

/**
 * Validate file format and structure
 */
export function validateFileFormat(
  content: string,
  format: 'csv' | 'json'
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (format === 'csv') {
    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) {
      errors.push('CSV file is empty');
    } else if (lines.length === 1) {
      errors.push('CSV file contains only header row');
    }
    
    // Check for consistent column count
    if (lines.length > 1) {
      const headerColumns = lines[0].split(',').length;
      for (let i = 1; i < lines.length; i++) {
        const rowColumns = lines[i].split(',').length;
        if (rowColumns !== headerColumns) {
          warnings.push(`Row ${i + 1} has ${rowColumns} columns, expected ${headerColumns}`);
        }
      }
    }
  } else if (format === 'json') {
    try {
      const parsed = JSON.parse(content);
      
      if (!Array.isArray(parsed) && !parsed.tools && !parsed.data) {
        errors.push('JSON must be an array or object with "tools" or "data" property');
      }
      
      const tools = Array.isArray(parsed) ? parsed : (parsed.tools || parsed.data);
      if (tools && tools.length === 0) {
        warnings.push('JSON contains no tools to import');
      }
    } catch (error) {
      errors.push(`Invalid JSON syntax: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
