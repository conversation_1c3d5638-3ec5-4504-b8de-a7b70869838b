/**
 * Synchronization Manager for Bulk Processing
 * Handles concurrent operations and prevents race conditions
 */

import { createClient } from '@supabase/supabase-js';
import { JobStatus } from '@/lib/types';

export interface AtomicUpdateResult {
  success: boolean;
  error?: string;
  jobId?: string;
  oldStatus?: string;
  newStatus?: string;
  oldVersion?: number;
  newVersion?: number;
  updatedAt?: string;
  currentVersion?: number;
}

export interface JobLock {
  jobId: string;
  lockId: string;
  acquiredAt: Date;
  expiresAt: Date;
}

/**
 * Synchronization Manager
 * Provides atomic operations and locking mechanisms for bulk processing
 */
export class SynchronizationManager {
  private supabase;
  private locks = new Map<string, JobLock>();
  private lockCleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    this.startLockCleanup();
  }

  /**
   * Atomically update job status with optimistic locking
   */
  async updateJobStatusAtomic(
    jobId: string, 
    newStatus: JobStatus, 
    expectedVersion?: number
  ): Promise<AtomicUpdateResult> {
    try {
      const { data, error } = await this.supabase.rpc('update_bulk_job_status_atomic', {
        p_job_id: jobId,
        p_new_status: newStatus,
        p_expected_version: expectedVersion || null
      });

      if (error) {
        console.error(`Atomic status update failed for job ${jobId}:`, error);
        return {
          success: false,
          error: error.message,
          jobId
        };
      }

      return data as AtomicUpdateResult;
    } catch (error) {
      console.error(`Atomic status update error for job ${jobId}:`, error);
      return {
        success: false,
        error: (error as Error).message,
        jobId
      };
    }
  }

  /**
   * Atomically update job progress
   */
  async updateJobProgressAtomic(
    jobId: string,
    processedItems: number,
    successfulItems: number,
    failedItems: number,
    expectedVersion?: number
  ): Promise<AtomicUpdateResult> {
    try {
      const { data, error } = await this.supabase.rpc('update_bulk_job_progress_atomic', {
        p_job_id: jobId,
        p_processed_items: processedItems,
        p_successful_items: successfulItems,
        p_failed_items: failedItems,
        p_expected_version: expectedVersion || null
      });

      if (error) {
        console.error(`Atomic progress update failed for job ${jobId}:`, error);
        return {
          success: false,
          error: error.message,
          jobId
        };
      }

      return data as AtomicUpdateResult;
    } catch (error) {
      console.error(`Atomic progress update error for job ${jobId}:`, error);
      return {
        success: false,
        error: (error as Error).message,
        jobId
      };
    }
  }

  /**
   * Atomically append to job progress log
   */
  async appendJobLogAtomic(
    jobId: string,
    logEntry: {
      timestamp: string;
      message: string;
      level: 'info' | 'warning' | 'error';
      data?: any;
    },
    expectedVersion?: number
  ): Promise<AtomicUpdateResult> {
    try {
      const { data, error } = await this.supabase.rpc('append_bulk_job_log_atomic', {
        p_job_id: jobId,
        p_log_entry: logEntry,
        p_expected_version: expectedVersion || null
      });

      if (error) {
        console.error(`Atomic log append failed for job ${jobId}:`, error);
        return {
          success: false,
          error: error.message,
          jobId
        };
      }

      return data as AtomicUpdateResult;
    } catch (error) {
      console.error(`Atomic log append error for job ${jobId}:`, error);
      return {
        success: false,
        error: (error as Error).message,
        jobId
      };
    }
  }

  /**
   * Atomically complete a job
   */
  async completeJobAtomic(
    jobId: string,
    expectedVersion?: number
  ): Promise<AtomicUpdateResult> {
    try {
      const { data, error } = await this.supabase.rpc('complete_bulk_job_atomic', {
        p_job_id: jobId,
        p_expected_version: expectedVersion || null
      });

      if (error) {
        console.error(`Atomic job completion failed for job ${jobId}:`, error);
        return {
          success: false,
          error: error.message,
          jobId
        };
      }

      return data as AtomicUpdateResult;
    } catch (error) {
      console.error(`Atomic job completion error for job ${jobId}:`, error);
      return {
        success: false,
        error: (error as Error).message,
        jobId
      };
    }
  }

  /**
   * Acquire a lock for a job
   */
  async acquireLock(jobId: string, timeoutMs: number = 300000): Promise<string | null> {
    const lockId = `lock_${jobId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const acquiredAt = new Date();
    const expiresAt = new Date(acquiredAt.getTime() + timeoutMs);

    // Check if job is already locked
    if (this.locks.has(jobId)) {
      const existingLock = this.locks.get(jobId)!;
      if (existingLock.expiresAt > new Date()) {
        console.log(`Job ${jobId} is already locked by ${existingLock.lockId}`);
        return null;
      } else {
        // Lock expired, remove it
        this.locks.delete(jobId);
      }
    }

    // Acquire lock
    const lock: JobLock = {
      jobId,
      lockId,
      acquiredAt,
      expiresAt
    };

    this.locks.set(jobId, lock);
    console.log(`🔒 Acquired lock ${lockId} for job ${jobId}`);
    
    return lockId;
  }

  /**
   * Release a lock for a job
   */
  releaseLock(jobId: string, lockId: string): boolean {
    const existingLock = this.locks.get(jobId);
    
    if (!existingLock) {
      console.log(`No lock found for job ${jobId}`);
      return false;
    }

    if (existingLock.lockId !== lockId) {
      console.log(`Lock ID mismatch for job ${jobId}: expected ${existingLock.lockId}, got ${lockId}`);
      return false;
    }

    this.locks.delete(jobId);
    console.log(`🔓 Released lock ${lockId} for job ${jobId}`);
    return true;
  }

  /**
   * Check if a job is locked
   */
  isLocked(jobId: string): boolean {
    const lock = this.locks.get(jobId);
    if (!lock) return false;

    // Check if lock is expired
    if (lock.expiresAt <= new Date()) {
      this.locks.delete(jobId);
      return false;
    }

    return true;
  }

  /**
   * Execute a function with a job lock
   */
  async withLock<T>(
    jobId: string,
    operation: () => Promise<T>,
    timeoutMs: number = 300000
  ): Promise<T> {
    const lockId = await this.acquireLock(jobId, timeoutMs);
    
    if (!lockId) {
      throw new Error(`Failed to acquire lock for job ${jobId}`);
    }

    try {
      const result = await operation();
      return result;
    } finally {
      this.releaseLock(jobId, lockId);
    }
  }

  /**
   * Retry operation with exponential backoff on version mismatch
   */
  async retryOnVersionMismatch<T>(
    operation: () => Promise<AtomicUpdateResult>,
    maxRetries: number = 3,
    baseDelayMs: number = 100
  ): Promise<AtomicUpdateResult> {
    let lastResult: AtomicUpdateResult;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      lastResult = await operation();
      
      if (lastResult.success || lastResult.error !== 'version_mismatch') {
        return lastResult;
      }

      // Wait before retrying with exponential backoff
      const delayMs = baseDelayMs * Math.pow(2, attempt);
      console.log(`Version mismatch, retrying in ${delayMs}ms (attempt ${attempt + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }

    return lastResult!;
  }

  /**
   * Get current job version from database
   */
  async getJobVersion(jobId: string): Promise<number | null> {
    try {
      const { data, error } = await this.supabase
        .from('bulk_processing_jobs')
        .select('version')
        .eq('id', jobId)
        .single();

      if (error || !data) {
        console.error(`Failed to get job version for ${jobId}:`, error);
        return null;
      }

      return data.version;
    } catch (error) {
      console.error(`Error getting job version for ${jobId}:`, error);
      return null;
    }
  }

  /**
   * Start periodic cleanup of expired locks
   */
  private startLockCleanup(): void {
    this.lockCleanupInterval = setInterval(() => {
      this.cleanupExpiredLocks();
    }, 60000); // Cleanup every minute
  }

  /**
   * Cleanup expired locks
   */
  private cleanupExpiredLocks(): void {
    const now = new Date();
    const expiredJobs: string[] = [];

    for (const [jobId, lock] of this.locks.entries()) {
      if (lock.expiresAt <= now) {
        expiredJobs.push(jobId);
      }
    }

    for (const jobId of expiredJobs) {
      this.locks.delete(jobId);
      console.log(`🧹 Cleaned up expired lock for job ${jobId}`);
    }

    if (expiredJobs.length > 0) {
      console.log(`🧹 Cleaned up ${expiredJobs.length} expired locks`);
    }
  }

  /**
   * Shutdown the synchronization manager
   */
  shutdown(): void {
    console.log('🛑 Shutting down synchronization manager...');
    
    if (this.lockCleanupInterval) {
      clearInterval(this.lockCleanupInterval);
      this.lockCleanupInterval = null;
    }

    // Release all locks
    this.locks.clear();
    
    console.log('✅ Synchronization manager shutdown complete');
  }
}

// Singleton instance
let syncManagerInstance: SynchronizationManager | null = null;

export function getSynchronizationManager(): SynchronizationManager {
  if (!syncManagerInstance) {
    syncManagerInstance = new SynchronizationManager();
  }
  return syncManagerInstance;
}
