/**
 * Race Condition Tests for Bulk Import System
 * Tests atomic operations and synchronization fixes
 */

import { getSynchronizationManager } from '@/lib/bulk-processing/sync-manager';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';

// Mock Supabase with atomic operations
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      insert: jest.fn().mockReturnValue({ error: null }),
      update: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({ error: null })
      }),
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({ data: [{ version: 1 }], error: null }),
        single: jest.fn().mockReturnValue({ data: { version: 1 }, error: null })
      }),
    })),
    rpc: jest.fn((functionName, params) => {
      // Mock atomic operations
      if (functionName === 'update_bulk_job_status_atomic') {
        return Promise.resolve({
          data: {
            success: true,
            jobId: params.p_job_id,
            oldStatus: 'processing',
            newStatus: params.p_new_status,
            oldVersion: 1,
            newVersion: 2,
            updatedAt: new Date().toISOString()
          },
          error: null
        });
      }
      
      if (functionName === 'update_bulk_job_progress_atomic') {
        return Promise.resolve({
          data: {
            success: true,
            jobId: params.p_job_id,
            newVersion: 2,
            updatedAt: new Date().toISOString()
          },
          error: null
        });
      }
      
      if (functionName === 'complete_bulk_job_atomic') {
        return Promise.resolve({
          data: {
            success: true,
            jobId: params.p_job_id,
            oldStatus: 'processing',
            newStatus: 'completed',
            newVersion: 2,
            updatedAt: new Date().toISOString()
          },
          error: null
        });
      }
      
      return Promise.resolve({ data: null, error: null });
    })
  })),
}));

// Mock job manager
jest.mock('@/lib/jobs', () => ({
  getJobManager: jest.fn(() => ({
    createJob: jest.fn(() => Promise.resolve({ id: 'test-job-123' })),
    getJob: jest.fn(() => Promise.resolve({ 
      id: 'test-job-123', 
      status: 'completed',
      result: { toolId: 'test-tool-123' }
    })),
  })),
}));

describe('Race Condition Tests', () => {
  let syncManager: any;
  let bulkEngine: any;

  beforeEach(() => {
    syncManager = getSynchronizationManager();
    bulkEngine = getBulkProcessingEngine();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    if (syncManager && typeof syncManager.shutdown === 'function') {
      syncManager.shutdown();
    }
    if (bulkEngine && typeof bulkEngine.shutdown === 'function') {
      await bulkEngine.shutdown();
    }
  });

  describe('Atomic Status Updates', () => {
    test('should update job status atomically', async () => {
      const jobId = 'test-job-123';
      const newStatus = 'completed';
      
      const result = await syncManager.updateJobStatusAtomic(jobId, newStatus, 1);
      
      expect(result.success).toBe(true);
      expect(result.jobId).toBe(jobId);
      expect(result.newStatus).toBe(newStatus);
      expect(result.newVersion).toBe(2);
    });

    test('should handle version mismatch gracefully', async () => {
      const jobId = 'test-job-456';
      
      // Mock version mismatch
      const mockRpc = jest.fn().mockResolvedValueOnce({
        data: {
          success: false,
          error: 'version_mismatch',
          currentVersion: 3
        },
        error: null
      }).mockResolvedValueOnce({
        data: {
          success: true,
          jobId,
          newVersion: 4
        },
        error: null
      });

      syncManager.supabase.rpc = mockRpc;
      
      const result = await syncManager.retryOnVersionMismatch(
        () => syncManager.updateJobStatusAtomic(jobId, 'completed', 2),
        2
      );
      
      expect(result.success).toBe(true);
      expect(mockRpc).toHaveBeenCalledTimes(2); // First call fails, second succeeds
    });

    test('should retry with exponential backoff on version mismatch', async () => {
      const jobId = 'test-job-789';
      let callCount = 0;
      
      const mockOperation = jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          return Promise.resolve({
            success: false,
            error: 'version_mismatch'
          });
        }
        return Promise.resolve({
          success: true,
          jobId
        });
      });
      
      const startTime = Date.now();
      const result = await syncManager.retryOnVersionMismatch(mockOperation, 3, 50);
      const endTime = Date.now();
      
      expect(result.success).toBe(true);
      expect(mockOperation).toHaveBeenCalledTimes(3);
      expect(endTime - startTime).toBeGreaterThan(100); // Should have delays
    });
  });

  describe('Job Locking', () => {
    test('should acquire and release locks correctly', async () => {
      const jobId = 'test-job-lock';
      
      // Acquire lock
      const lockId = await syncManager.acquireLock(jobId, 5000);
      expect(lockId).toBeDefined();
      expect(syncManager.isLocked(jobId)).toBe(true);
      
      // Release lock
      const released = syncManager.releaseLock(jobId, lockId!);
      expect(released).toBe(true);
      expect(syncManager.isLocked(jobId)).toBe(false);
    });

    test('should prevent concurrent lock acquisition', async () => {
      const jobId = 'test-job-concurrent';
      
      // First lock acquisition
      const lockId1 = await syncManager.acquireLock(jobId, 5000);
      expect(lockId1).toBeDefined();
      
      // Second lock acquisition should fail
      const lockId2 = await syncManager.acquireLock(jobId, 5000);
      expect(lockId2).toBeNull();
      
      // Release first lock
      syncManager.releaseLock(jobId, lockId1!);
      
      // Now second acquisition should succeed
      const lockId3 = await syncManager.acquireLock(jobId, 5000);
      expect(lockId3).toBeDefined();
      
      syncManager.releaseLock(jobId, lockId3!);
    });

    test('should execute operations with lock protection', async () => {
      const jobId = 'test-job-with-lock';
      let operationExecuted = false;
      
      const result = await syncManager.withLock(jobId, async () => {
        operationExecuted = true;
        return 'success';
      });
      
      expect(result).toBe('success');
      expect(operationExecuted).toBe(true);
      expect(syncManager.isLocked(jobId)).toBe(false); // Lock should be released
    });

    test('should handle lock timeout', async () => {
      const jobId = 'test-job-timeout';
      
      // Acquire lock with short timeout
      const lockId = await syncManager.acquireLock(jobId, 100);
      expect(lockId).toBeDefined();
      
      // Wait for timeout
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Lock should be expired
      expect(syncManager.isLocked(jobId)).toBe(false);
    });
  });

  describe('Concurrent Progress Updates', () => {
    test('should handle concurrent progress updates safely', async () => {
      const jobId = 'test-job-progress';
      
      // Simulate concurrent progress updates
      const updates = [
        { processed: 10, successful: 8, failed: 2 },
        { processed: 20, successful: 18, failed: 2 },
        { processed: 30, successful: 25, failed: 5 }
      ];
      
      const results = await Promise.all(
        updates.map(update => 
          syncManager.updateJobProgressAtomic(
            jobId,
            update.processed,
            update.successful,
            update.failed,
            1
          )
        )
      );
      
      // All updates should succeed (due to mocking)
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    test('should maintain data consistency during concurrent writes', async () => {
      const jobId = 'test-job-consistency';
      let sharedCounter = 0;
      
      // Simulate concurrent operations that modify shared state
      const operations = Array.from({ length: 5 }, (_, i) => 
        syncManager.withLock(jobId, async () => {
          const currentValue = sharedCounter;
          // Simulate some async work
          await new Promise(resolve => setTimeout(resolve, 10));
          sharedCounter = currentValue + 1;
          return sharedCounter;
        })
      );
      
      const results = await Promise.all(operations);
      
      // Final counter should be 5 (all operations executed sequentially)
      expect(sharedCounter).toBe(5);
      expect(results).toEqual([1, 2, 3, 4, 5]);
    });
  });

  describe('Version Tracking', () => {
    test('should track job versions correctly', async () => {
      const jobId = 'test-job-version';
      
      // Get initial version
      const initialVersion = await syncManager.getJobVersion(jobId);
      expect(initialVersion).toBe(1);
      
      // Update status (should increment version)
      const result = await syncManager.updateJobStatusAtomic(jobId, 'processing');
      expect(result.success).toBe(true);
      expect(result.newVersion).toBe(2);
    });

    test('should prevent updates with stale versions', async () => {
      const jobId = 'test-job-stale';
      
      // Mock version mismatch
      const mockRpc = jest.fn().mockResolvedValue({
        data: {
          success: false,
          error: 'version_mismatch',
          currentVersion: 5
        },
        error: null
      });

      syncManager.supabase.rpc = mockRpc;
      
      const result = await syncManager.updateJobStatusAtomic(jobId, 'completed', 3);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('version_mismatch');
      expect(result.currentVersion).toBe(5);
    });
  });

  describe('Cleanup and Shutdown', () => {
    test('should cleanup expired locks', async () => {
      const jobId = 'test-job-cleanup';
      
      // Acquire lock with very short timeout
      const lockId = await syncManager.acquireLock(jobId, 50);
      expect(lockId).toBeDefined();
      expect(syncManager.isLocked(jobId)).toBe(true);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Trigger cleanup
      syncManager.cleanupExpiredLocks();
      
      expect(syncManager.isLocked(jobId)).toBe(false);
    });

    test('should shutdown cleanly', () => {
      const jobId = 'test-job-shutdown';
      
      // Acquire some locks
      syncManager.acquireLock(jobId + '1', 5000);
      syncManager.acquireLock(jobId + '2', 5000);
      
      expect(syncManager.locks.size).toBe(2);
      
      // Shutdown
      syncManager.shutdown();
      
      expect(syncManager.locks.size).toBe(0);
    });
  });
});
