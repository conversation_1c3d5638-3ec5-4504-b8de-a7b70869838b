/**
 * @jest-environment jsdom
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { 
  createMockFile, 
  createCSVContent, 
  createJSONContent,
  createFormData,
  validateImportResult
} from './utils/test-helpers';
import { 
  validTestTools, 
  invalidTestTools, 
  importTestScenarios,
  csvTestData,
  jsonTestData
} from './fixtures/test-data';

// Mock File and FormData for Node.js environment
global.File = class MockFile {
  name: string;
  type: string;
  size: number;
  content: string;

  constructor(content: string[], filename: string, options: { type: string }) {
    this.name = filename;
    this.type = options.type;
    this.content = content.join('');
    this.size = this.content.length;
  }

  async text(): Promise<string> {
    return this.content;
  }
} as any;

global.FormData = class MockFormData {
  private data = new Map<string, any>();

  append(key: string, value: any): void {
    this.data.set(key, value);
  }

  get(key: string): any {
    return this.data.get(key);
  }

  has(key: string): boolean {
    return this.data.has(key);
  }
} as any;

describe('Bulk Import - Data Transformation', () => {
  describe('CSV to Internal Format Transformation', () => {
    test('should transform valid CSV data correctly', () => {
      const csvContent = createCSVContent(validTestTools);
      const lines = csvContent.split('\n');
      
      expect(lines.length).toBeGreaterThan(1); // Header + data rows
      expect(lines[0]).toContain('Name'); // Header contains Name
      expect(lines[1]).toContain('AI Writing Assistant'); // First tool name
    });

    test('should handle CSV field mapping correctly', () => {
      const csvWithMappedFields = `Tool Name,Tool Link,Tool Description,Category,Verified
AI Writer,/tools/ai-writer,Writing assistant,ai-tools,true`;
      
      const lines = csvWithMappedFields.split('\n');
      const headers = lines[0].split(',');
      const values = lines[1].split(',');
      
      expect(headers).toContain('Tool Name');
      expect(headers).toContain('Tool Link');
      expect(values).toContain('AI Writer');
      expect(values).toContain('/tools/ai-writer');
    });

    test('should convert CSV headers to snake_case', () => {
      const testHeaders = [
        'Tool Name', // -> tool_name
        'Is Verified', // -> is_verified
        'Category ID', // -> category_id
        'Short Description', // -> short_description
        'Logo URL' // -> logo_url
      ];
      
      testHeaders.forEach(header => {
        const snakeCase = header.toLowerCase()
          .replace(/\s+/g, '_')
          .replace(/[^a-z0-9_]/g, '');
        
        expect(snakeCase).toMatch(/^[a-z0-9_]+$/);
      });
    });

    test('should handle boolean field conversion', () => {
      const booleanTestCases = [
        { input: 'true', expected: true },
        { input: 'false', expected: false },
        { input: '1', expected: true },
        { input: '0', expected: false },
        { input: 'TRUE', expected: true },
        { input: 'FALSE', expected: false },
        { input: 'yes', expected: false }, // Only 'true' and '1' should be true
        { input: '', expected: false }
      ];
      
      booleanTestCases.forEach(({ input, expected }) => {
        const result = input.toLowerCase() === 'true' || input === '1';
        expect(result).toBe(expected);
      });
    });

    test('should handle JSON field parsing in CSV', () => {
      const jsonFields = [
        '{"type":"freemium","price":9.99}', // Valid JSON
        '{"features":["ai","grammar"]}', // Array JSON
        'invalid-json', // Invalid JSON - should remain as string
        '', // Empty - should be null
        'null' // String null - should be null
      ];
      
      jsonFields.forEach(field => {
        let parsed;
        try {
          parsed = field ? JSON.parse(field) : null;
        } catch {
          parsed = field; // Keep as string if parsing fails
        }
        
        if (field === '') {
          expect(parsed).toBeNull();
        } else if (field === 'invalid-json') {
          expect(parsed).toBe('invalid-json');
        } else if (field !== 'null') {
          expect(typeof parsed).toBe('object');
        }
      });
    });

    test('should handle quoted CSV values correctly', () => {
      const quotedValues = [
        '"Simple quoted value"',
        '"Value with, comma"',
        '"Value with ""escaped quotes"""',
        '"Multi-line\nvalue"',
        'Unquoted value'
      ];
      
      quotedValues.forEach(value => {
        // Simulate CSV parsing logic
        let parsed = value;
        if (value.startsWith('"') && value.endsWith('"')) {
          parsed = value.slice(1, -1).replace(/""/g, '"');
        }
        
        expect(typeof parsed).toBe('string');
        if (value.includes('""')) {
          expect(parsed).not.toContain('""');
        }
      });
    });
  });

  describe('JSON to Internal Format Transformation', () => {
    test('should transform JSON array format correctly', () => {
      const jsonContent = createJSONContent(validTestTools, 'array');
      const parsed = JSON.parse(jsonContent);
      
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed.length).toBe(validTestTools.length);
      expect(parsed[0]).toHaveProperty('name');
      expect(parsed[0]).toHaveProperty('link');
    });

    test('should transform JSON with tools property correctly', () => {
      const jsonContent = createJSONContent(validTestTools, 'tools');
      const parsed = JSON.parse(jsonContent);
      
      expect(parsed).toHaveProperty('tools');
      expect(Array.isArray(parsed.tools)).toBe(true);
      expect(parsed.tools.length).toBe(validTestTools.length);
    });

    test('should transform JSON with data property correctly', () => {
      const jsonContent = createJSONContent(validTestTools, 'data');
      const parsed = JSON.parse(jsonContent);
      
      expect(parsed).toHaveProperty('data');
      expect(Array.isArray(parsed.data)).toBe(true);
      expect(parsed.data.length).toBe(validTestTools.length);
    });

    test('should handle nested JSON objects correctly', () => {
      const toolWithNestedData = {
        name: 'Complex Tool',
        link: '/tools/complex',
        features: {
          ai: true,
          categories: ['writing', 'editing'],
          pricing: {
            free: true,
            premium: { price: 9.99, currency: 'USD' }
          }
        }
      };
      
      const jsonContent = JSON.stringify([toolWithNestedData]);
      const parsed = JSON.parse(jsonContent);
      
      expect(parsed[0].features).toBeInstanceOf(Object);
      expect(parsed[0].features.categories).toBeInstanceOf(Array);
      expect(parsed[0].features.pricing.premium.price).toBe(9.99);
    });

    test('should preserve data types in JSON transformation', () => {
      const typedTool = {
        name: 'Typed Tool',
        link: '/tools/typed',
        is_verified: true,
        is_claimed: false,
        price: 29.99,
        user_count: 1000,
        tags: ['ai', 'productivity'],
        metadata: null,
        created_at: '2024-01-01T00:00:00Z'
      };
      
      const jsonContent = JSON.stringify([typedTool]);
      const parsed = JSON.parse(jsonContent);
      const tool = parsed[0];
      
      expect(typeof tool.name).toBe('string');
      expect(typeof tool.is_verified).toBe('boolean');
      expect(typeof tool.price).toBe('number');
      expect(typeof tool.user_count).toBe('number');
      expect(Array.isArray(tool.tags)).toBe(true);
      expect(tool.metadata).toBeNull();
      expect(typeof tool.created_at).toBe('string');
    });
  });

  describe('Field Mapping and Validation', () => {
    test('should map common field variations correctly', () => {
      const fieldMappings = [
        { input: 'name', expected: 'name' },
        { input: 'tool_name', expected: 'tool_name' },
        { input: 'toolName', expected: 'toolName' },
        { input: 'Tool Name', expected: 'tool_name' },
        { input: 'link', expected: 'link' },
        { input: 'url', expected: 'url' },
        { input: 'website', expected: 'website' },
        { input: 'description', expected: 'description' },
        { input: 'desc', expected: 'desc' }
      ];
      
      fieldMappings.forEach(({ input, expected }) => {
        const mapped = input.toLowerCase().replace(/\s+/g, '_');
        if (input.includes(' ')) {
          expect(mapped).toBe(expected);
        } else {
          expect(mapped).toBe(input.toLowerCase());
        }
      });
    });

    test('should validate required fields during transformation', () => {
      const testCases = [
        { name: 'Valid Tool', link: '/tools/valid', isValid: true },
        { name: '', link: '/tools/empty-name', isValid: false },
        { name: 'Tool Without Link', link: '', isValid: false },
        { name: '', link: '', isValid: false }
      ];
      
      testCases.forEach(({ name, link, isValid }) => {
        const hasRequiredFields = !!(name && link);
        expect(hasRequiredFields).toBe(isValid);
      });
    });

    test('should generate slugs from names correctly', () => {
      const nameToSlugCases = [
        { name: 'AI Writing Assistant', expected: 'ai-writing-assistant' },
        { name: 'Code Generator Pro!', expected: 'code-generator-pro' },
        { name: 'Tool with   Spaces', expected: 'tool-with-spaces' },
        { name: 'Special@#$%Characters', expected: 'specialcharacters' },
        { name: 'UPPERCASE TOOL', expected: 'uppercase-tool' },
        { name: '123 Numeric Tool', expected: '123-numeric-tool' }
      ];
      
      nameToSlugCases.forEach(({ name, expected }) => {
        const slug = name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-+|-+$/g, '')
          .trim();
        
        expect(slug).toBe(expected);
      });
    });

    test('should handle camelCase to snake_case conversion', () => {
      const camelToSnakeCases = [
        { camel: 'isVerified', snake: 'is_verified' },
        { camel: 'shortDescription', snake: 'short_description' },
        { camel: 'logoUrl', snake: 'logo_url' }, // Fixed: logoURL -> logoUrl
        { camel: 'categoryId', snake: 'category_id' },
        { camel: 'createdAt', snake: 'created_at' }
      ];

      camelToSnakeCases.forEach(({ camel, snake }) => {
        const converted = camel.replace(/([A-Z])/g, '_$1').toLowerCase();
        expect(converted).toBe(snake);
      });
    });
  });

  describe('Data Sanitization', () => {
    test('should sanitize HTML content in descriptions', () => {
      const htmlInputs = [
        '<script>alert("xss")</script>',
        '<p>Valid paragraph</p>',
        'Normal text with <b>bold</b>',
        '<img src="x" onerror="alert(1)">',
        'Text with & entities &amp; symbols'
      ];
      
      htmlInputs.forEach(input => {
        // Basic HTML sanitization (remove script tags, keep safe tags)
        const sanitized = input
          .replace(/<script[^>]*>.*?<\/script>/gi, '')
          .replace(/on\w+="[^"]*"/gi, '');
        
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('onerror=');
      });
    });

    test('should trim whitespace from all string fields', () => {
      const stringFields = [
        '  Leading spaces',
        'Trailing spaces  ',
        '  Both sides  ',
        '\t\nWhitespace chars\r\n',
        'Normal text'
      ];
      
      stringFields.forEach(field => {
        const trimmed = field.trim();
        expect(trimmed).not.toMatch(/^\s/);
        expect(trimmed).not.toMatch(/\s$/);
      });
    });

    test('should normalize URLs and links', () => {
      const urlCases = [
        { input: '/tools/test', expected: '/tools/test' },
        { input: 'tools/test', expected: '/tools/test' },
        { input: 'https://example.com/tool', expected: 'https://example.com/tool' },
        { input: 'http://example.com', expected: 'http://example.com' },
        { input: 'example.com', expected: 'https://example.com' }
      ];
      
      urlCases.forEach(({ input, expected }) => {
        let normalized = input;
        
        // Add leading slash for relative paths
        if (!input.startsWith('/') && !input.includes('://')) {
          if (input.includes('.')) {
            normalized = `https://${input}`;
          } else {
            normalized = `/${input}`;
          }
        }
        
        expect(normalized).toBe(expected);
      });
    });
  });
});
