/**
 * @jest-environment jsdom
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  createMockFile, 
  createCSVContent, 
  createJSONContent,
  createFormData,
  validateImportResult,
  createTestRequest,
  mockDatabaseResponses
} from './utils/test-helpers';
import { 
  validTestTools, 
  invalidTestTools,
  duplicateTestTools,
  importTestScenarios,
  csvTestData,
  jsonTestData
} from './fixtures/test-data';

// Mock Next.js Request and Response
global.Request = class MockRequest {
  url: string;
  method: string;
  headers: Map<string, string>;
  body: any;

  constructor(url: string, init?: RequestInit) {
    this.url = url;
    this.method = init?.method || 'GET';
    this.headers = new Map();
    this.body = init?.body;
    
    if (init?.headers) {
      Object.entries(init.headers).forEach(([key, value]) => {
        this.headers.set(key, value as string);
      });
    }
  }

  async formData(): Promise<FormData> {
    return this.body as FormData;
  }

  async text(): Promise<string> {
    return this.body as string;
  }

  async json(): Promise<any> {
    return JSON.parse(this.body as string);
  }
} as any;

global.FormData = class MockFormData {
  private data = new Map<string, any>();

  append(key: string, value: any): void {
    this.data.set(key, value);
  }

  get(key: string): any {
    return this.data.get(key);
  }

  has(key: string): boolean {
    return this.data.has(key);
  }
} as any;

global.File = class MockFile {
  name: string;
  type: string;
  size: number;
  content: string;

  constructor(content: string[], filename: string, options: { type: string }) {
    this.name = filename;
    this.type = options.type;
    this.content = content.join('');
    this.size = this.content.length;
  }

  async text(): Promise<string> {
    return this.content;
  }
} as any;

// Mock API functions
const mockGetAdminTools = jest.fn();
const mockCreateTool = jest.fn();
const mockValidateApiKey = jest.fn();

jest.mock('@/lib/supabase', () => ({
  getAdminTools: mockGetAdminTools,
  createTool: mockCreateTool
}));

jest.mock('@/lib/auth', () => ({
  validateApiKey: mockValidateApiKey
}));

describe('Bulk Import - End-to-End Workflow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockValidateApiKey.mockResolvedValue(true);
    mockGetAdminTools.mockResolvedValue({ data: mockDatabaseResponses.existingTools });
    mockCreateTool.mockResolvedValue(mockDatabaseResponses.createToolSuccess);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete CSV Import Workflow', () => {
    test('should successfully import valid CSV file', async () => {
      const csvContent = createCSVContent(validTestTools);
      const file = createMockFile(csvContent, 'tools.csv', 'text/csv');
      const formData = createFormData(file, { duplicateStrategy: 'skip' });
      
      // Mock successful import
      mockCreateTool.mockResolvedValue({ data: { id: 'new-tool-id' }, error: null });
      
      // Simulate API call
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      
      // Verify request structure
      expect(request.method).toBe('POST');
      expect(request.headers.get('x-api-key')).toBe('test-admin-key');
      
      const requestFormData = await request.formData();
      const uploadedFile = requestFormData.get('file') as any;
      
      expect(uploadedFile.name).toBe('tools.csv');
      expect(uploadedFile.type).toBe('text/csv');
      expect(await uploadedFile.text()).toBe(csvContent);
    });

    test('should handle CSV preview mode correctly', async () => {
      const csvContent = createCSVContent(validTestTools.slice(0, 2));
      const file = createMockFile(csvContent, 'preview.csv', 'text/csv');
      const formData = createFormData(file, { preview: true });
      
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      const requestFormData = await request.formData();
      
      expect(requestFormData.get('preview')).toBe('true');
      expect(requestFormData.has('duplicateStrategy')).toBe(false);
    });

    test('should validate CSV file size limits', async () => {
      const largeCsvContent = 'Name,Link,Description\n' + 'Tool,/tools/tool,Description\n'.repeat(100000);
      const largeFile = createMockFile(largeCsvContent, 'large.csv', 'text/csv');
      
      const maxSize = 10 * 1024 * 1024; // 10MB
      const isWithinLimit = largeFile.size <= maxSize;
      
      if (!isWithinLimit) {
        expect(largeFile.size).toBeGreaterThan(maxSize);
      }
    });

    test('should handle CSV parsing errors gracefully', async () => {
      const malformedCsv = 'Name,Link,Description\n"Unclosed quote,/tools/test,Description';
      const file = createMockFile(malformedCsv, 'malformed.csv', 'text/csv');
      
      // CSV parsing should handle malformed content
      const lines = malformedCsv.split('\n');
      expect(lines.length).toBeGreaterThan(1);
      
      // Even malformed CSV should be processable to some degree
      const headers = lines[0].split(',');
      expect(headers).toContain('Name');
    });
  });

  describe('Complete JSON Import Workflow', () => {
    test('should successfully import valid JSON file', async () => {
      const jsonContent = createJSONContent(validTestTools, 'tools');
      const file = createMockFile(jsonContent, 'tools.json', 'application/json');
      const formData = createFormData(file, { duplicateStrategy: 'error' });
      
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      const requestFormData = await request.formData();
      const uploadedFile = requestFormData.get('file') as any;
      
      expect(uploadedFile.name).toBe('tools.json');
      expect(uploadedFile.type).toBe('application/json');
      
      const parsedContent = JSON.parse(await uploadedFile.text());
      expect(parsedContent).toHaveProperty('tools');
      expect(parsedContent.tools).toHaveLength(validTestTools.length);
    });

    test('should handle different JSON formats', async () => {
      const formats = [
        { format: 'array', content: createJSONContent(validTestTools, 'array') },
        { format: 'tools', content: createJSONContent(validTestTools, 'tools') },
        { format: 'data', content: createJSONContent(validTestTools, 'data') }
      ];
      
      formats.forEach(({ format, content }) => {
        const parsed = JSON.parse(content);
        
        switch (format) {
          case 'array':
            expect(Array.isArray(parsed)).toBe(true);
            break;
          case 'tools':
            expect(parsed).toHaveProperty('tools');
            expect(Array.isArray(parsed.tools)).toBe(true);
            break;
          case 'data':
            expect(parsed).toHaveProperty('data');
            expect(Array.isArray(parsed.data)).toBe(true);
            break;
        }
      });
    });

    test('should validate JSON file size limits', async () => {
      const largeJsonContent = JSON.stringify({
        tools: Array.from({ length: 50000 }, (_, i) => ({
          name: `Tool ${i}`,
          link: `/tools/tool-${i}`,
          description: `Description for tool ${i}`
        }))
      });
      
      const largeFile = createMockFile(largeJsonContent, 'large.json', 'application/json');
      const maxSize = 50 * 1024 * 1024; // 50MB
      
      // Should be within JSON size limit
      expect(largeFile.size).toBeLessThan(maxSize);
    });

    test('should handle JSON syntax errors', async () => {
      const invalidJson = '{"tools": [{"name": "Test"'; // Missing closing brackets
      
      expect(() => JSON.parse(invalidJson)).toThrow();
      
      // Error should be caught and handled gracefully in actual implementation
      try {
        JSON.parse(invalidJson);
      } catch (error) {
        expect(error).toBeInstanceOf(SyntaxError);
      }
    });
  });

  describe('Duplicate Detection Workflow', () => {
    test('should detect and handle duplicates with skip strategy', async () => {
      const scenario = importTestScenarios.find(s => s.name === 'Duplicate Detection - Skip Strategy');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        const jsonContent = createJSONContent(scenario.data, 'tools');
        const file = createMockFile(jsonContent, 'duplicates.json', 'application/json');
        
        // Mock existing tools that will cause duplicates
        mockGetAdminTools.mockResolvedValue({ 
          data: [
            { name: 'AI Writing Assistant', slug: 'ai-writing-assistant' },
            { name: 'Code Generator Pro', slug: 'code-generator-pro' }
          ]
        });
        
        const formData = createFormData(file, { duplicateStrategy: 'skip' });
        const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
        
        // Verify duplicate strategy is set
        const requestFormData = await request.formData();
        expect(requestFormData.get('duplicateStrategy')).toBe('skip');
      }
    });

    test('should detect and handle duplicates with error strategy', async () => {
      const scenario = importTestScenarios.find(s => s.name === 'Duplicate Detection - Error Strategy');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        const csvContent = createCSVContent(scenario.data);
        const file = createMockFile(csvContent, 'duplicates.csv', 'text/csv');
        const formData = createFormData(file, { duplicateStrategy: 'error' });
        
        const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
        const requestFormData = await request.formData();
        
        expect(requestFormData.get('duplicateStrategy')).toBe('error');
      }
    });
  });

  describe('Error Handling Workflow', () => {
    test('should handle validation errors gracefully', async () => {
      const scenario = importTestScenarios.find(s => s.name === 'Invalid Data - Validation Errors');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        const jsonContent = createJSONContent(scenario.data, 'tools');
        const file = createMockFile(jsonContent, 'invalid.json', 'application/json');
        const formData = createFormData(file);
        
        // Verify invalid data structure
        const parsed = JSON.parse(jsonContent);
        const invalidItems = parsed.tools.filter((tool: any) => !tool.name || !tool.link);
        
        expect(invalidItems.length).toBeGreaterThan(0);
      }
    });

    test('should handle database errors during import', async () => {
      const validTool = validTestTools[0];
      const jsonContent = createJSONContent([validTool], 'tools');
      const file = createMockFile(jsonContent, 'db-error.json', 'application/json');
      
      // Mock database error
      mockCreateTool.mockResolvedValue({
        data: null,
        error: new Error('Database constraint violation')
      });
      
      const formData = createFormData(file);
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      
      // Verify error handling setup
      expect(mockCreateTool).toBeDefined();
    });

    test('should handle authentication failures', async () => {
      const jsonContent = createJSONContent(validTestTools.slice(0, 1), 'tools');
      const file = createMockFile(jsonContent, 'auth-fail.json', 'application/json');
      
      // Mock authentication failure
      mockValidateApiKey.mockResolvedValue(false);
      
      const formData = createFormData(file);
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      
      // Should fail authentication
      expect(await mockValidateApiKey(request)).toBe(false);
    });
  });

  describe('Mixed Scenario Workflow', () => {
    test('should handle complex mixed import scenario', async () => {
      const scenario = importTestScenarios.find(s => s.name === 'Mixed Scenario - Valid, Invalid, and Duplicates');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        const csvContent = createCSVContent(scenario.data);
        const file = createMockFile(csvContent, 'mixed.csv', 'text/csv');
        const formData = createFormData(file, { duplicateStrategy: scenario.duplicateStrategy });
        
        // Setup mixed responses
        mockGetAdminTools.mockResolvedValue({ data: mockDatabaseResponses.existingTools });
        
        // Some tools succeed, some fail
        mockCreateTool
          .mockResolvedValueOnce({ data: { id: 'tool-1' }, error: null })
          .mockResolvedValueOnce({ data: { id: 'tool-2' }, error: null })
          .mockResolvedValueOnce({ data: { id: 'tool-3' }, error: null })
          .mockResolvedValueOnce({ data: null, error: new Error('Validation failed') });
        
        const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
        
        // Verify mixed scenario setup
        expect(scenario.expectedResults.imported).toBe(3);
        expect(scenario.expectedResults.errors).toBe(5);
        expect(scenario.expectedResults.duplicates).toBe(2);
      }
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large file imports efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        name: `Tool ${i}`,
        link: `/tools/tool-${i}`,
        description: `Description for tool ${i}`,
        category_id: 'ai-tools'
      }));
      
      const jsonContent = createJSONContent(largeDataset, 'tools');
      const file = createMockFile(jsonContent, 'large-import.json', 'application/json');
      
      const startTime = Date.now();
      
      // Simulate processing
      const parsed = JSON.parse(jsonContent);
      const processedCount = parsed.tools.length;
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      expect(processedCount).toBe(1000);
      expect(processingTime).toBeLessThan(1000); // Should process quickly
    });

    test('should handle concurrent import requests', async () => {
      const concurrentRequests = Array.from({ length: 5 }, (_, i) => {
        const tools = validTestTools.slice(0, 2);
        const jsonContent = createJSONContent(tools, 'tools');
        const file = createMockFile(jsonContent, `concurrent-${i}.json`, 'application/json');
        const formData = createFormData(file);
        
        return createTestRequest('/api/admin/tools/import', 'POST', formData);
      });
      
      // All requests should be properly formed
      expect(concurrentRequests).toHaveLength(5);
      
      concurrentRequests.forEach((request, i) => {
        expect(request.method).toBe('POST');
        expect(request.headers.get('x-api-key')).toBe('test-admin-key');
      });
    });
  });

  describe('Integration with Editorial Workflow', () => {
    test('should create tools with correct submission metadata', async () => {
      const tool = validTestTools[0];
      const jsonContent = createJSONContent([tool], 'tools');
      const file = createMockFile(jsonContent, 'editorial.json', 'application/json');
      
      // Mock tool creation with editorial metadata
      mockCreateTool.mockImplementation((toolData) => {
        expect(toolData).toHaveProperty('submissionType', 'admin');
        expect(toolData).toHaveProperty('submissionSource', 'import');
        expect(toolData).toHaveProperty('contentStatus', 'draft');
        
        return Promise.resolve({ data: { id: 'tool-1', ...toolData }, error: null });
      });
      
      const formData = createFormData(file);
      const request = createTestRequest('/api/admin/tools/import', 'POST', formData);
      
      // Verify editorial integration setup
      expect(request).toBeDefined();
    });

    test('should handle content status transitions correctly', async () => {
      const toolsWithStatuses = [
        { ...validTestTools[0], content_status: 'draft' },
        { ...validTestTools[1], content_status: 'published' },
        { ...validTestTools[2], content_status: 'under_review' }
      ];
      
      const jsonContent = createJSONContent(toolsWithStatuses, 'tools');
      const file = createMockFile(jsonContent, 'statuses.json', 'application/json');
      
      const parsed = JSON.parse(jsonContent);
      const statuses = parsed.tools.map((tool: any) => tool.content_status);
      
      expect(statuses).toContain('draft');
      expect(statuses).toContain('published');
      expect(statuses).toContain('under_review');
    });
  });
});
