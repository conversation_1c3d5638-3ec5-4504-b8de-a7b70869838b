/**
 * @jest-environment jsdom
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { 
  createMockFile, 
  createCSVContent, 
  createJSONContent,
  validateImportResult,
  mockDatabaseResponses
} from './utils/test-helpers';
import { 
  validTestTools, 
  duplicateTestTools, 
  importTestScenarios
} from './fixtures/test-data';

// Mock Supabase for database operations
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn()
      }))
    })),
    insert: jest.fn(() => ({
      select: jest.fn()
    })),
    update: jest.fn(() => ({
      eq: jest.fn()
    }))
  }))
};

jest.mock('@/lib/supabase', () => ({
  getAdminTools: jest.fn(),
  createTool: jest.fn()
}));

describe('Bulk Import - Duplicate Detection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Name-based Duplicate Detection', () => {
    test('should detect exact name duplicates', () => {
      const existingTools = [
        { name: 'AI Writing Assistant', slug: 'ai-writing-assistant' },
        { name: 'Code Generator Pro', slug: 'code-generator-pro' }
      ];
      
      const newTool = { name: 'AI Writing Assistant', link: '/tools/duplicate' };
      
      const existingNames = new Set(existingTools.map(t => t.name.toLowerCase()));
      const isDuplicate = existingNames.has(newTool.name.toLowerCase());
      
      expect(isDuplicate).toBe(true);
    });

    test('should detect case-insensitive name duplicates', () => {
      const existingTools = [
        { name: 'AI Writing Assistant', slug: 'ai-writing-assistant' }
      ];
      
      const testCases = [
        'ai writing assistant',
        'AI WRITING ASSISTANT', 
        'Ai Writing Assistant',
        'AI Writing assistant'
      ];
      
      const existingNames = new Set(existingTools.map(t => t.name.toLowerCase()));
      
      testCases.forEach(testName => {
        const isDuplicate = existingNames.has(testName.toLowerCase());
        expect(isDuplicate).toBe(true);
      });
    });

    test('should not flag similar but different names as duplicates', () => {
      const existingTools = [
        { name: 'AI Writing Assistant', slug: 'ai-writing-assistant' }
      ];
      
      const similarNames = [
        'AI Writing Helper',
        'Writing Assistant AI',
        'AI Writer Assistant',
        'AI Writing Tool'
      ];
      
      const existingNames = new Set(existingTools.map(t => t.name.toLowerCase()));
      
      similarNames.forEach(name => {
        const isDuplicate = existingNames.has(name.toLowerCase());
        expect(isDuplicate).toBe(false);
      });
    });

    test('should handle names with special characters', () => {
      const existingTools = [
        { name: 'AI Writer Pro™', slug: 'ai-writer-pro' },
        { name: 'Code Gen 2.0', slug: 'code-gen-20' }
      ];
      
      const testCases = [
        { name: 'AI Writer Pro™', shouldBeDuplicate: true },
        { name: 'AI Writer Pro', shouldBeDuplicate: false },
        { name: 'Code Gen 2.0', shouldBeDuplicate: true },
        { name: 'Code Gen 2', shouldBeDuplicate: false }
      ];
      
      const existingNames = new Set(existingTools.map(t => t.name.toLowerCase()));
      
      testCases.forEach(({ name, shouldBeDuplicate }) => {
        const isDuplicate = existingNames.has(name.toLowerCase());
        expect(isDuplicate).toBe(shouldBeDuplicate);
      });
    });
  });

  describe('Slug-based Duplicate Detection', () => {
    test('should detect exact slug duplicates', () => {
      const existingTools = [
        { name: 'AI Writing Assistant', slug: 'ai-writing-assistant' },
        { name: 'Code Generator Pro', slug: 'code-generator-pro' }
      ];
      
      const newTool = { name: 'Different Name', link: '/tools/ai-writing-assistant' };
      const newSlug = newTool.link.replace('/tools/', '');
      
      const existingSlugs = new Set(existingTools.map(t => t.slug));
      const isDuplicate = existingSlugs.has(newSlug);
      
      expect(isDuplicate).toBe(true);
    });

    test('should generate consistent slugs from names', () => {
      const nameToSlugCases = [
        { name: 'AI Writing Assistant', expectedSlug: 'ai-writing-assistant' },
        { name: 'Code Generator Pro!', expectedSlug: 'code-generator-pro' },
        { name: 'Tool with   Multiple   Spaces', expectedSlug: 'tool-with-multiple-spaces' },
        { name: 'Special@#$%Characters', expectedSlug: 'specialcharacters' }
      ];
      
      nameToSlugCases.forEach(({ name, expectedSlug }) => {
        const generatedSlug = name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-+|-+$/g, '')
          .trim();
        
        expect(generatedSlug).toBe(expectedSlug);
      });
    });

    test('should detect slug conflicts from different names', () => {
      const existingTools = [
        { name: 'AI Writer', slug: 'ai-writer' }
      ];

      // Test cases that should generate similar slugs
      const testCases = [
        { name: 'AI Writer!', expectedSlug: 'ai-writer' },
        { name: 'AI   Writer', expectedSlug: 'ai-writer' },
        { name: 'AI@Writer', expectedSlug: 'aiwriter' }, // No space, so no hyphen
      ];

      const existingSlugs = new Set(existingTools.map(t => t.slug));

      testCases.forEach(({ name, expectedSlug }) => {
        const generatedSlug = name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '') // Remove special chars except spaces and hyphens
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with single
          .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
          .trim();

        expect(generatedSlug).toBe(expectedSlug);

        // Check if it conflicts with existing slugs
        const isDuplicate = existingSlugs.has(generatedSlug);
        if (expectedSlug === 'ai-writer') {
          expect(isDuplicate).toBe(true); // Should conflict
        } else {
          expect(isDuplicate).toBe(false); // Should not conflict
        }
      });
    });

    test('should handle URL-based slug extraction', () => {
      const urlToSlugCases = [
        { url: '/tools/ai-writer', expectedSlug: 'ai-writer' },
        { url: '/tools/code-gen-pro', expectedSlug: 'code-gen-pro' },
        { url: 'tools/no-leading-slash', expectedSlug: 'no-leading-slash' },
        { url: '/tools/trailing-slash/', expectedSlug: 'trailing-slash' }
      ];
      
      urlToSlugCases.forEach(({ url, expectedSlug }) => {
        const extractedSlug = url
          .replace(/^\/+/, '')
          .replace(/\/+$/, '')
          .replace('tools/', '');
        
        expect(extractedSlug).toBe(expectedSlug);
      });
    });
  });

  describe('Duplicate Handling Strategies', () => {
    test('should skip duplicates with skip strategy', () => {
      const scenario = importTestScenarios.find(s => s.name === 'Duplicate Detection - Skip Strategy');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        expect(scenario.duplicateStrategy).toBe('skip');
        expect(scenario.expectedResults.skipped).toBe(2);
        expect(scenario.expectedResults.errors).toBe(0);
        expect(scenario.expectedResults.duplicates).toBe(2);
      }
    });

    test('should error on duplicates with error strategy', () => {
      const scenario = importTestScenarios.find(s => s.name === 'Duplicate Detection - Error Strategy');
      expect(scenario).toBeDefined();
      
      if (scenario) {
        expect(scenario.duplicateStrategy).toBe('error');
        expect(scenario.expectedResults.skipped).toBe(0);
        expect(scenario.expectedResults.errors).toBe(2);
        expect(scenario.expectedResults.duplicates).toBe(2);
      }
    });

    test('should track duplicate information correctly', () => {
      const duplicateInfo = {
        row: 5,
        name: 'AI Writing Assistant',
        slug: 'ai-writing-assistant'
      };
      
      expect(duplicateInfo).toHaveProperty('row');
      expect(duplicateInfo).toHaveProperty('name');
      expect(duplicateInfo).toHaveProperty('slug');
      expect(typeof duplicateInfo.row).toBe('number');
      expect(typeof duplicateInfo.name).toBe('string');
      expect(typeof duplicateInfo.slug).toBe('string');
    });

    test('should handle update strategy (future implementation)', () => {
      // Test structure for future update strategy implementation
      const updateStrategy = 'update';
      const duplicateHandling = {
        skip: (duplicate: any) => ({ action: 'skip', duplicate }),
        error: (duplicate: any) => ({ action: 'error', duplicate }),
        update: (duplicate: any) => ({ action: 'update', duplicate })
      };
      
      const result = duplicateHandling[updateStrategy as keyof typeof duplicateHandling];
      expect(result).toBeDefined();
      
      const testDuplicate = { name: 'Test Tool', slug: 'test-tool' };
      const handlingResult = result(testDuplicate);
      expect(handlingResult.action).toBe('update');
      expect(handlingResult.duplicate).toBe(testDuplicate);
    });
  });

  describe('Cross-Import Duplicate Detection', () => {
    test('should detect duplicates within the same import batch', () => {
      const importBatch = [
        { name: 'Tool A', link: '/tools/tool-a' },
        { name: 'Tool B', link: '/tools/tool-b' },
        { name: 'Tool A', link: '/tools/tool-a-duplicate' }, // Duplicate name
        { name: 'Tool C', link: '/tools/tool-a' } // Duplicate slug
      ];
      
      const seenNames = new Set<string>();
      const seenSlugs = new Set<string>();
      const duplicates: any[] = [];
      
      importBatch.forEach((tool, index) => {
        const slug = tool.link.replace('/tools/', '');
        
        if (seenNames.has(tool.name.toLowerCase()) || seenSlugs.has(slug)) {
          duplicates.push({ row: index + 1, tool });
        } else {
          seenNames.add(tool.name.toLowerCase());
          seenSlugs.add(slug);
        }
      });
      
      expect(duplicates).toHaveLength(2);
      expect(duplicates[0].row).toBe(3); // Third tool (duplicate name)
      expect(duplicates[1].row).toBe(4); // Fourth tool (duplicate slug)
    });

    test('should maintain duplicate tracking across processing', () => {
      const existingTools = mockDatabaseResponses.existingTools;
      const newTools = [
        { name: 'New Tool', link: '/tools/new-tool' },
        { name: 'AI Writing Assistant', link: '/tools/duplicate-name' }, // Duplicate existing name
        { name: 'Another Tool', link: '/tools/code-generator-pro' } // Duplicate existing slug
      ];
      
      const existingNames = new Set(existingTools.map(t => t.name.toLowerCase()));
      const existingSlugs = new Set(existingTools.map(t => t.slug));
      
      const results = newTools.map((tool, index) => {
        const slug = tool.link.replace('/tools/', '');
        const isDuplicateName = existingNames.has(tool.name.toLowerCase());
        const isDuplicateSlug = existingSlugs.has(slug);
        
        return {
          row: index + 1,
          tool,
          isDuplicate: isDuplicateName || isDuplicateSlug,
          duplicateType: isDuplicateName ? 'name' : isDuplicateSlug ? 'slug' : null
        };
      });
      
      const duplicates = results.filter(r => r.isDuplicate);
      expect(duplicates).toHaveLength(2);
      expect(duplicates[0].duplicateType).toBe('name');
      expect(duplicates[1].duplicateType).toBe('slug');
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle large datasets efficiently', () => {
      const largeExistingSet = Array.from({ length: 10000 }, (_, i) => ({
        name: `Tool ${i}`,
        slug: `tool-${i}`
      }));
      
      const existingNames = new Set(largeExistingSet.map(t => t.name.toLowerCase()));
      const existingSlugs = new Set(largeExistingSet.map(t => t.slug));
      
      // Test lookup performance
      const startTime = Date.now();
      
      const testLookups = [
        'Tool 5000',
        'Tool 9999',
        'Non-existent Tool',
        'tool-5000',
        'tool-9999',
        'non-existent-slug'
      ];
      
      testLookups.forEach(lookup => {
        existingNames.has(lookup.toLowerCase());
        existingSlugs.has(lookup);
      });
      
      const endTime = Date.now();
      const lookupTime = endTime - startTime;
      
      // Should complete lookups quickly (under 10ms for this test)
      expect(lookupTime).toBeLessThan(10);
    });

    test('should handle empty existing datasets', () => {
      const emptyNames = new Set<string>();
      const emptySlugs = new Set<string>();
      
      const newTool = { name: 'First Tool', link: '/tools/first-tool' };
      const slug = newTool.link.replace('/tools/', '');
      
      const isDuplicateName = emptyNames.has(newTool.name.toLowerCase());
      const isDuplicateSlug = emptySlugs.has(slug);
      
      expect(isDuplicateName).toBe(false);
      expect(isDuplicateSlug).toBe(false);
    });

    test('should handle null and undefined values gracefully', () => {
      const toolsWithNulls = [
        { name: null, link: '/tools/null-name' },
        { name: 'Valid Tool', link: null },
        { name: undefined, link: '/tools/undefined-name' },
        { name: 'Another Tool', link: undefined }
      ];

      toolsWithNulls.forEach(tool => {
        const hasValidName = !!(tool.name && typeof tool.name === 'string');
        const hasValidLink = !!(tool.link && typeof tool.link === 'string');

        // Should be flagged as invalid, not processed for duplicates
        const isValid = hasValidName && hasValidLink;
        expect(isValid).toBe(false);
      });
    });

    test('should handle extremely long names and slugs', () => {
      const longName = 'A'.repeat(1000);
      const longSlug = 'a'.repeat(1000);
      
      const existingNames = new Set([longName.toLowerCase()]);
      const existingSlugs = new Set([longSlug]);
      
      const isDuplicateName = existingNames.has(longName.toLowerCase());
      const isDuplicateSlug = existingSlugs.has(longSlug);
      
      expect(isDuplicateName).toBe(true);
      expect(isDuplicateSlug).toBe(true);
    });
  });
});
