-- Clear Editorial Reviews SQL Script
-- 
-- This script safely deletes all data from the editorial_reviews table
-- and resets any related foreign key references in the tools table.
-- 
-- Usage: Execute this SQL in your Supabase SQL editor or via psql

-- =====================================================
-- STEP 1: Show current state
-- =====================================================

-- Count existing editorial reviews
SELECT 'Editorial Reviews Count' as info, COUNT(*) as count FROM editorial_reviews;

-- Count tools with editorial_review_id references
SELECT 'Tools with Editorial Review References' as info, COUNT(*) as count 
FROM tools 
WHERE editorial_review_id IS NOT NULL;

-- Count AI jobs with malformed IDs
SELECT 'AI Jobs with Malformed IDs' as info, COUNT(*) as count
FROM ai_generation_jobs
WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36);

-- Show malformed AI job IDs
SELECT 'Malformed AI Job IDs:' as info, id, tool_id, status
FROM ai_generation_jobs
WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36)
ORDER BY created_at DESC;

-- =====================================================
-- STEP 2: Clear tool references first (to avoid FK violations)
-- =====================================================

-- Clear editorial_review_id references in tools table
UPDATE tools 
SET editorial_review_id = NULL 
WHERE editorial_review_id IS NOT NULL;

-- Clear ai_generation_job_id references for malformed jobs
UPDATE tools
SET ai_generation_job_id = NULL
WHERE ai_generation_job_id IN (
    SELECT id FROM ai_generation_jobs
    WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36)
);

-- =====================================================
-- STEP 3: Delete editorial reviews
-- =====================================================

-- Delete all editorial reviews
DELETE FROM editorial_reviews;

-- =====================================================
-- STEP 4: Delete malformed AI jobs
-- =====================================================

-- Delete AI jobs with malformed IDs
DELETE FROM ai_generation_jobs
WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36);

-- =====================================================
-- STEP 5: Verify cleanup
-- =====================================================

-- Verify editorial reviews are deleted
SELECT 'Remaining Editorial Reviews' as info, COUNT(*) as count FROM editorial_reviews;

-- Verify tool references are cleared
SELECT 'Tools with Editorial Review References' as info, COUNT(*) as count 
FROM tools 
WHERE editorial_review_id IS NOT NULL;

-- Verify malformed AI jobs are deleted
SELECT 'Remaining Malformed AI Jobs' as info, COUNT(*) as count
FROM ai_generation_jobs
WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36);

-- Show remaining AI jobs count
SELECT 'Total Remaining AI Jobs' as info, COUNT(*) as count FROM ai_generation_jobs;

-- =====================================================
-- STEP 6: Optional - Reset sequences (if needed)
-- =====================================================

-- Note: UUID columns don't use sequences, but if you have any auto-increment
-- columns in related tables, you might want to reset them here

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check for any orphaned references
SELECT 'Orphaned Tool References' as check_type, 
       t.id as tool_id, 
       t.name as tool_name,
       t.editorial_review_id,
       t.ai_generation_job_id
FROM tools t
LEFT JOIN editorial_reviews er ON t.editorial_review_id = er.id
LEFT JOIN ai_generation_jobs aj ON t.ai_generation_job_id = aj.id
WHERE (t.editorial_review_id IS NOT NULL AND er.id IS NULL)
   OR (t.ai_generation_job_id IS NOT NULL AND aj.id IS NULL);

-- Final summary
SELECT
    'CLEANUP SUMMARY' as summary,
    (SELECT COUNT(*) FROM editorial_reviews) as editorial_reviews_remaining,
    (SELECT COUNT(*) FROM ai_generation_jobs WHERE id::text LIKE '%ai_job_ai_job_%' OR (id::text LIKE 'ai_job_%' AND LENGTH(id::text) > 36)) as malformed_jobs_remaining,
    (SELECT COUNT(*) FROM tools WHERE editorial_review_id IS NOT NULL) as tools_with_editorial_refs,
    (SELECT COUNT(*) FROM ai_generation_jobs) as total_ai_jobs_remaining;

-- =====================================================
-- COMMENTS
-- =====================================================

/*
This script performs the following cleanup operations:

1. ASSESSMENT: Shows current state of data
2. REFERENCE CLEANUP: Clears foreign key references in tools table
3. DATA DELETION: Removes all editorial reviews and malformed AI jobs
4. VERIFICATION: Confirms cleanup was successful
5. ORPHAN CHECK: Identifies any remaining orphaned references

The script is designed to be safe and provides verification at each step.
All operations are logged with descriptive output.

IMPORTANT NOTES:
- This script deletes data permanently
- Make sure to backup your database before running
- The script handles foreign key constraints properly
- All malformed UUID references are cleaned up

MALFORMED ID PATTERNS DETECTED:
- IDs containing "ai_job_ai_job_" (double prefix)
- IDs starting with "ai_job_" but longer than 36 characters
- These patterns cause UUID parsing errors in PostgreSQL

After running this script, the editorial system should work without
UUID parsing errors.
*/
