/**
 * @jest-environment jsdom
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import {
  createMockFile,
  createCSVContent,
  createJSONContent,
  generateTestFileWithSize,
  createMalformedContent,
  validateFileFormat
} from './utils/test-helpers';
import {
  validTestTools,
  invalidTestTools,
  csvTestData,
  jsonTestData
} from './fixtures/test-data';

// Mock File constructor for Node.js environment
global.File = class MockFile {
  name: string;
  type: string;
  size: number;
  content: string;

  constructor(content: string[], filename: string, options: { type: string }) {
    this.name = filename;
    this.type = options.type;
    this.content = content.join('');
    this.size = this.content.length;
  }

  async text(): Promise<string> {
    return this.content;
  }
} as any;

// Mock Blob for Node.js environment
global.Blob = class MockBlob {
  size: number;
  type: string;
  content: string;

  constructor(content: string[], options: { type: string }) {
    this.content = content.join('');
    this.size = this.content.length;
    this.type = options.type;
  }
} as any;

describe('Bulk Import - File Upload Validation', () => {
  describe('File Type Validation', () => {
    test('should accept valid CSV files', () => {
      const csvContent = createCSVContent(validTestTools);
      const file = createMockFile(csvContent, 'tools.csv', 'text/csv');
      
      expect(file.name).toBe('tools.csv');
      expect(file.type).toBe('text/csv');
      expect(file.size).toBeGreaterThan(0);
    });

    test('should accept valid JSON files', () => {
      const jsonContent = createJSONContent(validTestTools);
      const file = createMockFile(jsonContent, 'tools.json', 'application/json');
      
      expect(file.name).toBe('tools.json');
      expect(file.type).toBe('application/json');
      expect(file.size).toBeGreaterThan(0);
    });

    test('should reject unsupported file types', () => {
      const invalidExtensions = ['txt', 'xlsx', 'pdf', 'doc'];
      
      invalidExtensions.forEach(ext => {
        const file = createMockFile('content', `file.${ext}`, `application/${ext}`);
        const isValidExtension = ['csv', 'json'].includes(ext);
        expect(isValidExtension).toBe(false);
      });
    });

    test('should validate file extension matches content type', () => {
      // CSV file with JSON content type
      const csvFile = createMockFile('Name,Link\nTool,/tool', 'tools.csv', 'application/json');
      expect(csvFile.name.endsWith('.csv')).toBe(true);
      expect(csvFile.type).toBe('application/json'); // Mismatch
      
      // JSON file with CSV content type  
      const jsonFile = createMockFile('{"tools":[]}', 'tools.json', 'text/csv');
      expect(jsonFile.name.endsWith('.json')).toBe(true);
      expect(jsonFile.type).toBe('text/csv'); // Mismatch
    });
  });

  describe('File Size Validation', () => {
    test('should accept files within size limits', () => {
      // CSV limit: 10MB, JSON limit: 50MB
      const smallCSV = generateTestFileWithSize(1024 * 1024, 'csv'); // 1MB
      const smallJSON = generateTestFileWithSize(5 * 1024 * 1024, 'json'); // 5MB
      
      expect(smallCSV.size).toBeLessThan(10 * 1024 * 1024);
      expect(smallJSON.size).toBeLessThan(50 * 1024 * 1024);
    });

    test('should reject CSV files exceeding 10MB limit', () => {
      const largeCSV = generateTestFileWithSize(11 * 1024 * 1024, 'csv'); // 11MB
      expect(largeCSV.size).toBeGreaterThan(10 * 1024 * 1024);
    });

    test('should reject JSON files exceeding 50MB limit', () => {
      const largeJSON = generateTestFileWithSize(51 * 1024 * 1024, 'json'); // 51MB
      expect(largeJSON.size).toBeGreaterThan(50 * 1024 * 1024);
    });

    test('should handle empty files', () => {
      const emptyCSV = createMockFile('', 'empty.csv', 'text/csv');
      const emptyJSON = createMockFile('', 'empty.json', 'application/json');
      
      expect(emptyCSV.size).toBe(0);
      expect(emptyJSON.size).toBe(0);
    });
  });

  describe('CSV Format Validation', () => {
    test('should validate well-formed CSV content', () => {
      const validation = validateFileFormat(csvTestData.validCSV, 'csv');
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect CSV with missing headers', () => {
      const validation = validateFileFormat(csvTestData.headerOnlyCSV, 'csv');
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('CSV file contains only header row');
    });

    test('should detect empty CSV files', () => {
      const validation = validateFileFormat('', 'csv');
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('CSV file is empty');
    });

    test('should handle CSV with inconsistent column counts', () => {
      const inconsistentCSV = `Name,Link,Description
Tool 1,/tools/1
Tool 2,/tools/2,Description,Extra Column`;
      
      const validation = validateFileFormat(inconsistentCSV, 'csv');
      
      expect(validation.warnings.length).toBeGreaterThan(0);
      expect(validation.warnings.some(w => w.includes('columns'))).toBe(true);
    });

    test('should handle CSV with quoted values', () => {
      const quotedCSV = `Name,Link,Description
"Tool with, comma","/tools/comma-tool","Description with ""quotes"""
Normal Tool,/tools/normal,Normal description`;
      
      const validation = validateFileFormat(quotedCSV, 'csv');
      expect(validation.isValid).toBe(true);
    });

    test('should detect malformed CSV with unclosed quotes', () => {
      const malformedCSV = createMalformedContent('csv', 'unclosed-quotes');
      const validation = validateFileFormat(malformedCSV, 'csv');
      
      // CSV parsing should still work, but may produce warnings
      expect(validation.isValid).toBe(true); // CSV is more forgiving
    });
  });

  describe('JSON Format Validation', () => {
    test('should validate well-formed JSON array', () => {
      const jsonContent = createJSONContent(validTestTools, 'array');
      const validation = validateFileFormat(jsonContent, 'json');
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should validate JSON with tools property', () => {
      const jsonContent = createJSONContent(validTestTools, 'tools');
      const validation = validateFileFormat(jsonContent, 'json');
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should validate JSON with data property', () => {
      const jsonContent = createJSONContent(validTestTools, 'data');
      const validation = validateFileFormat(jsonContent, 'json');
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should reject JSON with invalid structure', () => {
      const invalidJSON = JSON.stringify(jsonTestData.invalidJSONStructure);
      const validation = validateFileFormat(invalidJSON, 'json');
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('JSON must be an array or object with "tools" or "data" property');
    });

    test('should detect malformed JSON syntax', () => {
      const malformedJSON = createMalformedContent('json', 'syntax-error');
      const validation = validateFileFormat(malformedJSON, 'json');
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(e => e.includes('Invalid JSON syntax'))).toBe(true);
    });

    test('should handle empty JSON arrays', () => {
      const emptyJSON = JSON.stringify([]);
      const validation = validateFileFormat(emptyJSON, 'json');
      
      expect(validation.isValid).toBe(true);
      expect(validation.warnings).toContain('JSON contains no tools to import');
    });

    test('should handle non-object JSON values', () => {
      const nonObjectJSON = '"just a string"';
      const validation = validateFileFormat(nonObjectJSON, 'json');
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('JSON must be an array or object with "tools" or "data" property');
    });
  });

  describe('Content Structure Validation', () => {
    test('should validate required fields in CSV', () => {
      const csvWithMissingFields = `Name,Description
,Missing name field
Tool Without Link,Has description but no link`;
      
      const validation = validateFileFormat(csvWithMissingFields, 'csv');
      expect(validation.isValid).toBe(true); // Structure is valid, content validation happens later
    });

    test('should validate required fields in JSON', () => {
      const jsonWithMissingFields = JSON.stringify([
        { name: 'Tool 1' }, // Missing link
        { link: '/tools/2' }, // Missing name
        { name: 'Tool 3', link: '/tools/3' } // Valid
      ]);
      
      const validation = validateFileFormat(jsonWithMissingFields, 'json');
      expect(validation.isValid).toBe(true); // Structure is valid, content validation happens later
    });

    test('should handle special characters in content', () => {
      const specialCharsCSV = `Name,Link,Description
"Tool with émojis 🚀","/tools/emoji-tool","Description with special chars: àáâãäå"
"Tool with newlines","/tools/newline-tool","Description with
actual newlines"`;
      
      const validation = validateFileFormat(specialCharsCSV, 'csv');
      expect(validation.isValid).toBe(true);
    });

    test('should handle large field values', () => {
      const longDescription = 'A'.repeat(10000); // 10KB description
      const largeFieldCSV = `Name,Link,Description
Tool with large field,/tools/large,"${longDescription}"`;
      
      const validation = validateFileFormat(largeFieldCSV, 'csv');
      expect(validation.isValid).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    test('should handle files with BOM (Byte Order Mark)', () => {
      const bomCSV = '\uFEFFName,Link,Description\nTool,/tools/tool,Description';
      const validation = validateFileFormat(bomCSV, 'csv');
      
      expect(validation.isValid).toBe(true);
    });

    test('should handle different line endings', () => {
      const windowsLineEndings = 'Name,Link\r\nTool,/tools/tool\r\n';
      const unixLineEndings = 'Name,Link\nTool,/tools/tool\n';
      const macLineEndings = 'Name,Link\rTool,/tools/tool\r';

      [windowsLineEndings, unixLineEndings, macLineEndings].forEach(content => {
        // Normalize line endings for testing
        const normalizedContent = content.replace(/\r\n|\r|\n/g, '\n');
        const lines = normalizedContent.split('\n').filter(line => line.trim());

        // Should have header and data rows
        expect(lines.length).toBeGreaterThanOrEqual(2);
        expect(lines[0]).toContain('Name');
        expect(lines[1]).toContain('Tool');
      });
    });

    test('should handle mixed content encodings', () => {
      const mixedEncodingCSV = `Name,Link,Description
"Tool with UTF-8: 中文","/tools/chinese","Description with émojis: 🎉"
"Tool with symbols: ©®™","/tools/symbols","Mathematical symbols: ∑∏∆"`;
      
      const validation = validateFileFormat(mixedEncodingCSV, 'csv');
      expect(validation.isValid).toBe(true);
    });
  });
});
