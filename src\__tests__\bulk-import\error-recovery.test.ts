/**
 * Error Recovery Tests for Bulk Import System
 * Tests transaction rollbacks, partial failure recovery, and retry mechanisms
 */

import { getErrorRecoveryManager } from '@/lib/bulk-processing/error-recovery';
import { getTransactionManager } from '@/lib/bulk-processing/transaction-manager';

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      insert: jest.fn().mockReturnValue({ data: [{ id: 'test-id' }], error: null }),
      update: jest.fn().mockReturnValue({ error: null }),
      delete: jest.fn().mockReturnValue({ error: null }),
      select: jest.fn().mockReturnValue({
        match: jest.fn().mockReturnValue({ data: [{ id: 'test-id', name: 'test' }], error: null })
      })
    })),
    rpc: jest.fn().mockReturnValue({ data: { success: true }, error: null })
  })),
}));

describe('Error Recovery Tests', () => {
  let errorRecoveryManager: any;
  let transactionManager: any;

  beforeEach(() => {
    errorRecoveryManager = getErrorRecoveryManager();
    transactionManager = getTransactionManager();
    jest.clearAllMocks();
  });

  describe('Data Validation', () => {
    test('should validate data with comprehensive error reporting', () => {
      const schema = {
        required: ['name', 'url'],
        properties: {
          name: { type: 'string', maxLength: 10 },
          url: { type: 'string', format: 'url' },
          description: { type: 'string', maxLength: 50, truncate: true }
        }
      };

      const invalidData = {
        name: 123, // Wrong type
        url: 'invalid-url', // Invalid URL
        description: 'This is a very long description that exceeds the maximum length limit'
      };

      const result = errorRecoveryManager.validateData(invalidData, schema);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2); // name type error, url format error
      expect(result.warnings).toHaveLength(1); // description truncation warning
      expect(result.errors[0].field).toBe('name');
      expect(result.errors[1].field).toBe('url');
      expect(result.warnings[0].field).toBe('description');
    });

    test('should sanitize valid data correctly', () => {
      const schema = {
        required: ['name'],
        properties: {
          name: { type: 'string', maxLength: 20 },
          url: { type: 'string', format: 'url' },
          description: { type: 'string', maxLength: 100, truncate: true }
        }
      };

      const validData = {
        name: 'Test Tool',
        url: 'https://example.com',
        description: 'A test tool for validation'
      };

      const result = errorRecoveryManager.validateData(validData, schema);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
      expect(result.sanitizedData).toEqual(validData);
    });

    test('should handle missing required fields', () => {
      const schema = {
        required: ['name', 'url'],
        properties: {
          name: { type: 'string' },
          url: { type: 'string', format: 'url' }
        }
      };

      const incompleteData = {
        name: 'Test Tool'
        // Missing url
      };

      const result = errorRecoveryManager.validateData(incompleteData, schema);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].field).toBe('url');
      expect(result.errors[0].error).toContain('Required field');
    });
  });

  describe('Retry Mechanisms', () => {
    test('should retry operations with exponential backoff', async () => {
      let attemptCount = 0;
      const mockOperation = jest.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('network_timeout');
        }
        return Promise.resolve('success');
      });

      const result = await errorRecoveryManager.retryWithBackoff(mockOperation, {
        maxRetries: 3,
        baseDelayMs: 10, // Short delay for testing
        retryableErrors: ['network_timeout']
      });

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    test('should not retry non-retryable errors', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('validation_error'));

      await expect(
        errorRecoveryManager.retryWithBackoff(mockOperation, {
          maxRetries: 3,
          retryableErrors: ['network_timeout']
        })
      ).rejects.toThrow('validation_error');

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    test('should respect max retries limit', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('network_timeout'));

      await expect(
        errorRecoveryManager.retryWithBackoff(mockOperation, {
          maxRetries: 2,
          baseDelayMs: 10,
          retryableErrors: ['network_timeout']
        })
      ).rejects.toThrow('network_timeout');

      expect(mockOperation).toHaveBeenCalledTimes(2);
    });
  });

  describe('Recovery Context Validation', () => {
    test('should validate recovery context correctly', async () => {
      const validContext = {
        jobId: 'test-job-123',
        operationType: 'tool_creation' as const,
        partialData: { toolId: 'partial-tool' },
        failurePoint: 'ai_generation',
        error: 'API timeout',
        timestamp: new Date(),
        retryCount: 1,
        maxRetries: 3
      };

      const result = await errorRecoveryManager.recoverFromFailure(validContext);

      expect(result.success).toBeDefined();
      expect(result.recovered).toBeDefined();
      expect(result.partialSuccess).toBeDefined();
    });

    test('should reject invalid recovery context', async () => {
      const invalidContext = {
        // Missing required fields
        operationType: 'tool_creation' as const,
        retryCount: -1, // Invalid retry count
        maxRetries: -1  // Invalid max retries
      };

      const result = await errorRecoveryManager.recoverFromFailure(invalidContext as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid recovery context');
      expect(result.nextAction).toBe('abort');
    });
  });

  describe('Partial Failure Recovery', () => {
    test('should recover from tool creation failure with partial data', async () => {
      const context = {
        jobId: 'test-job-123',
        operationType: 'tool_creation' as const,
        partialData: { toolId: 'partial-tool-123' },
        failurePoint: 'media_collection',
        error: 'network_timeout',
        timestamp: new Date(),
        retryCount: 1,
        maxRetries: 3
      };

      const result = await errorRecoveryManager.recoverFromFailure(context);

      expect(result.success).toBe(true);
      expect(result.recovered).toBe(true);
      expect(result.partialSuccess).toBe(true);
      expect(result.savedData).toEqual({ toolId: 'partial-tool-123' });
      expect(result.nextAction).toBe('retry');
    });

    test('should handle AI generation failure with partial content', async () => {
      const context = {
        jobId: 'test-job-456',
        operationType: 'ai_generation' as const,
        partialData: { 
          generatedContent: { 
            description: 'Partial description',
            features: ['feature1', 'feature2']
          }
        },
        failurePoint: 'pricing_generation',
        error: 'rate_limit_exceeded',
        timestamp: new Date(),
        retryCount: 2,
        maxRetries: 3
      };

      const result = await errorRecoveryManager.recoverFromFailure(context);

      expect(result.success).toBe(true);
      expect(result.recovered).toBe(true);
      expect(result.partialSuccess).toBe(true);
      expect(result.nextAction).toBe('retry');
    });

    test('should handle max retries exceeded', async () => {
      const context = {
        jobId: 'test-job-789',
        operationType: 'media_collection' as const,
        partialData: { mediaAssets: { favicon: 'favicon.ico' } },
        failurePoint: 'screenshot_capture',
        error: 'network_timeout',
        timestamp: new Date(),
        retryCount: 3,
        maxRetries: 3
      };

      const result = await errorRecoveryManager.recoverFromFailure(context);

      expect(result.success).toBe(false);
      expect(result.recovered).toBe(false);
      expect(result.partialSuccess).toBe(true);
      expect(result.nextAction).toBe('manual_review');
      expect(result.error).toContain('Max retries');
    });
  });

  describe('Transaction Management', () => {
    test('should begin and execute transaction successfully', async () => {
      const transactionId = await transactionManager.beginTransaction();
      expect(transactionId).toBeDefined();

      // Add operations
      const opId1 = transactionManager.addOperation(transactionId, {
        type: 'insert',
        table: 'tools',
        data: { name: 'Test Tool', url: 'https://example.com' }
      });

      const opId2 = transactionManager.addOperation(transactionId, {
        type: 'update',
        table: 'tools',
        data: { status: 'active' },
        conditions: { id: 'test-id' }
      });

      expect(opId1).toBeDefined();
      expect(opId2).toBeDefined();

      // Execute transaction
      const result = await transactionManager.executeTransaction(transactionId);

      expect(result.success).toBe(true);
      expect(result.operationsExecuted).toBe(2);
    });

    test('should rollback transaction on failure', async () => {
      const transactionId = await transactionManager.beginTransaction();

      // Add operations that will fail
      transactionManager.addOperation(transactionId, {
        type: 'insert',
        table: 'tools',
        data: { name: 'Test Tool' }
      });

      // Mock a failure
      const mockExecuteOperation = jest.spyOn(transactionManager as any, 'executeOperation')
        .mockRejectedValueOnce(new Error('Database error'));

      const result = await transactionManager.executeTransaction(transactionId);

      expect(result.success).toBe(false);
      expect(result.rollbackPerformed).toBe(true);
      expect(result.error).toContain('Database error');

      mockExecuteOperation.mockRestore();
    });

    test('should prevent operations on non-pending transactions', () => {
      const transactionId = 'invalid-transaction';

      expect(() => {
        transactionManager.addOperation(transactionId, {
          type: 'insert',
          table: 'tools',
          data: { name: 'Test' }
        });
      }).toThrow('Transaction invalid-transaction not found');
    });
  });

  describe('Error Classification', () => {
    test('should correctly identify retryable errors', () => {
      const retryableErrors = [
        'network_timeout',
        'rate_limit_exceeded',
        'temporary_unavailable',
        'connection_error',
        'api_timeout'
      ];

      retryableErrors.forEach(error => {
        expect(errorRecoveryManager.isRetryableError(error)).toBe(true);
      });
    });

    test('should correctly identify non-retryable errors', () => {
      const nonRetryableErrors = [
        'validation_error',
        'authentication_failed',
        'permission_denied',
        'invalid_data',
        'schema_violation'
      ];

      nonRetryableErrors.forEach(error => {
        expect(errorRecoveryManager.isRetryableError(error)).toBe(false);
      });
    });
  });
});
