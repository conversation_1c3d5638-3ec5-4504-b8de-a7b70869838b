/**
 * Memory Management Tests for Bulk Import System
 * Tests memory leak fixes and resource cleanup
 */

import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';
import { getJobMemoryManager } from '@/lib/jobs/memory-manager';
import { BulkToolData } from '@/lib/types';

// Mock Supabase with proper chaining
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      insert: jest.fn().mockReturnValue({
        data: [{ id: 'test-job-123' }],
        error: null
      }),
      update: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          data: [{ id: 'test-job-123' }],
          error: null
        })
      }),
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          data: [{
            id: 'test-job-123',
            job_type: 'manual_entry',
            status: 'completed',
            total_items: 1,
            processed_items: 1,
            successful_items: 1,
            failed_items: 0,
            source_data: {},
            processing_options: {},
            progress_log: [],
            created_by: 'test-user',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }],
          error: null
        }),
        single: jest.fn().mockReturnValue({
          data: {
            id: 'test-job-123',
            job_type: 'manual_entry',
            status: 'completed',
            total_items: 1,
            processed_items: 1,
            successful_items: 1,
            failed_items: 0,
            source_data: {},
            processing_options: {},
            progress_log: [],
            created_by: 'test-user',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          error: null
        })
      }),
    })),
    rpc: jest.fn().mockReturnValue({
      data: { success: true, newVersion: 2 },
      error: null
    })
  })),
}));

// Mock job manager
jest.mock('@/lib/jobs', () => ({
  getJobManager: jest.fn(() => ({
    createJob: jest.fn(() => Promise.resolve({ id: 'test-job-123' })),
    getJob: jest.fn(() => Promise.resolve({ 
      id: 'test-job-123', 
      status: 'completed',
      result: { toolId: 'test-tool-123' }
    })),
  })),
}));

describe('Memory Management Tests', () => {
  let bulkEngine: any;
  let memoryManager: any;

  beforeEach(() => {
    bulkEngine = getBulkProcessingEngine();
    memoryManager = getJobMemoryManager();
    
    // Clear any existing state
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Cleanup after each test
    if (bulkEngine && typeof bulkEngine.shutdown === 'function') {
      await bulkEngine.shutdown();
    }
    if (memoryManager && typeof memoryManager.shutdown === 'function') {
      memoryManager.shutdown();
    }
  });

  describe('Job Memory Cleanup', () => {
    test('should cleanup job from activeJobs Map on completion', async () => {
      const testData: BulkToolData[] = [
        {
          url: 'https://example.com/tool1',
          providedData: { name: 'Test Tool 1' },
          needsGeneration: { name: false, description: true, features: true, pricing: true, prosAndCons: true, haiku: true, hashtags: true }
        }
      ];

      const options = {
        batchSize: 1,
        delayBetweenBatches: 0,
        retryAttempts: 1,
        aiProvider: 'openai' as const,
        skipExisting: false,
        maxProcessingTime: 10000, // 10 seconds for test
      };

      // Create bulk job
      const job = await bulkEngine.createBulkJob(testData, options, {
        jobType: 'manual_entry',
        submittedBy: 'test-user'
      });

      // Verify job is in activeJobs
      expect(bulkEngine.activeJobs.has(job.id)).toBe(true);

      // Cancel job to trigger cleanup (using public method)
      await bulkEngine.cancelBulkJob(job.id);

      // Verify job is cleaned up from activeJobs
      expect(bulkEngine.activeJobs.has(job.id)).toBe(false);
    });

    test('should cleanup job from activeJobs Map on failure', async () => {
      const testData: BulkToolData[] = [
        {
          url: 'invalid-url',
          providedData: {},
          needsGeneration: { name: true, description: true, features: true, pricing: true, prosAndCons: true, haiku: true, hashtags: true }
        }
      ];

      const options = {
        batchSize: 1,
        delayBetweenBatches: 0,
        retryAttempts: 1,
        aiProvider: 'openai' as const,
        skipExisting: false,
        maxProcessingTime: 5000,
      };

      // Create bulk job
      const job = await bulkEngine.createBulkJob(testData, options, {
        jobType: 'manual_entry',
        submittedBy: 'test-user'
      });

      // Verify job is in activeJobs
      expect(bulkEngine.activeJobs.has(job.id)).toBe(true);

      // Cancel job to simulate cleanup (using public method)
      await bulkEngine.cancelBulkJob(job.id);

      // Verify job is cleaned up from activeJobs
      expect(bulkEngine.activeJobs.has(job.id)).toBe(false);
    });

    test('should cleanup job resources on cancellation', async () => {
      const testData: BulkToolData[] = [
        {
          url: 'https://example.com/tool1',
          providedData: {},
          needsGeneration: { name: true, description: true, features: true, pricing: true, prosAndCons: true, haiku: true, hashtags: true }
        }
      ];

      const options = {
        batchSize: 1,
        delayBetweenBatches: 0,
        retryAttempts: 1,
        aiProvider: 'openai' as const,
        skipExisting: false,
      };

      // Create bulk job
      const job = await bulkEngine.createBulkJob(testData, options, {
        jobType: 'manual_entry',
        submittedBy: 'test-user'
      });

      // Verify job is in activeJobs
      expect(bulkEngine.activeJobs.has(job.id)).toBe(true);

      // Cancel job
      await bulkEngine.cancelBulkJob(job.id);

      // Verify job is cleaned up
      expect(bulkEngine.activeJobs.has(job.id)).toBe(false);
    });
  });

  describe('Resource Management', () => {
    test('should register and cleanup job resources', async () => {
      const jobId = 'test-job-123';

      // Register a resource
      const resourceId = memoryManager.registerResource(jobId, {
        id: 'test-resource',
        type: 'temp_file',
        data: { path: '/tmp/test-file.txt' },
        createdAt: new Date(),
      });

      // Verify resource is registered
      expect(resourceId).toBeDefined();
      expect(memoryManager.jobResources.has(jobId)).toBe(true);

      // Cleanup job resources
      await memoryManager.cleanupJobResources(jobId);

      // Verify resources are cleaned up
      expect(memoryManager.jobResources.has(jobId)).toBe(false);
    });

    test('should handle multiple resource types', async () => {
      const jobId = 'test-job-456';

      // Register different types of resources
      const tempFileId = memoryManager.registerResource(jobId, {
        id: 'temp-file',
        type: 'temp_file',
        data: { path: '/tmp/test.txt' },
        createdAt: new Date(),
      });

      const cacheId = memoryManager.registerResource(jobId, {
        id: 'cache-entry',
        type: 'cache_entry',
        data: { key: 'test-cache-key' },
        createdAt: new Date(),
      });

      const timerId = memoryManager.registerResource(jobId, {
        id: 'timer',
        type: 'timer',
        data: { timerId: setTimeout(() => {}, 1000) },
        createdAt: new Date(),
      });

      // Verify all resources are registered
      expect(memoryManager.jobResources.get(jobId)?.size).toBe(3);

      // Cleanup all resources
      await memoryManager.cleanupJobResources(jobId);

      // Verify all resources are cleaned up
      expect(memoryManager.jobResources.has(jobId)).toBe(false);
    });
  });

  describe('Memory Monitoring', () => {
    test('should provide memory statistics', () => {
      const stats = memoryManager.getMemoryStats();
      
      expect(stats).toHaveProperty('heapUsed');
      expect(stats).toHaveProperty('heapTotal');
      expect(stats).toHaveProperty('external');
      expect(stats).toHaveProperty('activeResources');
      expect(stats).toHaveProperty('activeJobs');
      
      expect(typeof stats.heapUsed).toBe('number');
      expect(typeof stats.heapTotal).toBe('number');
      expect(typeof stats.external).toBe('number');
      expect(typeof stats.activeResources).toBe('number');
      expect(typeof stats.activeJobs).toBe('number');
    });

    test('should track resource count correctly', async () => {
      const jobId = 'test-job-789';
      const initialStats = memoryManager.getMemoryStats();

      // Register resources
      memoryManager.registerResource(jobId, {
        id: 'resource-1',
        type: 'temp_file',
        data: { path: '/tmp/test1.txt' },
        createdAt: new Date(),
      });

      memoryManager.registerResource(jobId, {
        id: 'resource-2',
        type: 'cache_entry',
        data: { key: 'test-key' },
        createdAt: new Date(),
      });

      const afterStats = memoryManager.getMemoryStats();

      // Verify resource count increased
      expect(afterStats.activeResources).toBe(initialStats.activeResources + 2);
      expect(afterStats.activeJobs).toBe(initialStats.activeJobs + 1);

      // Cleanup and verify count decreased
      await memoryManager.cleanupJobResources(jobId);
      const finalStats = memoryManager.getMemoryStats();

      expect(finalStats.activeResources).toBe(initialStats.activeResources);
      expect(finalStats.activeJobs).toBe(initialStats.activeJobs);
    });
  });

  describe('Timeout Management', () => {
    test('should setup and clear job timeouts', async () => {
      const testData: BulkToolData[] = [
        {
          url: 'https://example.com/tool1',
          providedData: {},
          needsGeneration: { name: true, description: true, features: true, pricing: true, prosAndCons: true, haiku: true, hashtags: true }
        }
      ];

      const options = {
        batchSize: 1,
        delayBetweenBatches: 0,
        retryAttempts: 1,
        aiProvider: 'openai' as const,
        skipExisting: false,
        maxProcessingTime: 1000, // 1 second timeout
      };

      // Create bulk job
      const job = await bulkEngine.createBulkJob(testData, options, {
        jobType: 'manual_entry',
        submittedBy: 'test-user'
      });

      // Verify timeout is set
      expect(bulkEngine.jobTimeouts.has(job.id)).toBe(true);

      // Cancel job to trigger cleanup
      await bulkEngine.cancelBulkJob(job.id);

      // Verify timeout is cleared
      expect(bulkEngine.jobTimeouts.has(job.id)).toBe(false);
    });
  });

  describe('Shutdown Cleanup', () => {
    test('should cleanup all resources on shutdown', async () => {
      const jobId = 'test-job-shutdown';
      
      // Register some resources
      memoryManager.registerResource(jobId, {
        id: 'resource-1',
        type: 'temp_file',
        data: { path: '/tmp/test.txt' },
        createdAt: new Date(),
      });

      // Verify resources exist
      expect(memoryManager.jobResources.has(jobId)).toBe(true);

      // Shutdown memory manager
      memoryManager.shutdown();

      // Verify all resources are cleaned up
      expect(memoryManager.jobResources.size).toBe(0);
      expect(memoryManager.resources.size).toBe(0);
    });
  });
});
