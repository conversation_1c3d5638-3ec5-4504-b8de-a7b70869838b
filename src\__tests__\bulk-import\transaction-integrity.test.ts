/**
 * @jest-environment node
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  createMockFile, 
  createJSONContent,
  mockDatabaseResponses
} from './utils/test-helpers';
import { 
  validTestTools, 
  transactionTestData
} from './fixtures/test-data';

// Mock Supabase client with transaction support
const mockSupabaseTransaction = {
  from: jest.fn(),
  rpc: jest.fn(),
  auth: { getUser: jest.fn() }
};

const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(),
        limit: jest.fn(() => ({
          single: jest.fn()
        }))
      })),
      order: jest.fn(() => ({
        limit: jest.fn()
      }))
    })),
    insert: jest.fn(() => ({
      select: jest.fn(),
      single: jest.fn()
    })),
    update: jest.fn(() => ({
      eq: jest.fn(),
      select: jest.fn()
    })),
    delete: jest.fn(() => ({
      eq: jest.fn()
    }))
  })),
  rpc: jest.fn(),
  auth: { getUser: jest.fn() }
};

// Mock database transaction utilities
const mockTransaction = {
  begin: jest.fn(),
  commit: jest.fn(),
  rollback: jest.fn(),
  savepoint: jest.fn(),
  release: jest.fn()
};

jest.mock('@/lib/supabase', () => ({
  supabase: mockSupabase,
  getAdminTools: jest.fn(),
  createTool: jest.fn()
}));

describe('Bulk Import - Transaction Integrity', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset transaction state
    mockTransaction.begin.mockResolvedValue(undefined);
    mockTransaction.commit.mockResolvedValue(undefined);
    mockTransaction.rollback.mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Transaction Lifecycle', () => {
    test('should begin transaction before bulk import', async () => {
      const importData = validTestTools.slice(0, 2);
      
      // Simulate transaction begin
      await mockTransaction.begin();
      
      expect(mockTransaction.begin).toHaveBeenCalledTimes(1);
    });

    test('should commit transaction after successful import', async () => {
      const importData = validTestTools.slice(0, 2);
      
      // Simulate successful import process
      await mockTransaction.begin();
      
      // Mock successful tool creation for each item
      for (const tool of importData) {
        mockSupabase.from().insert().select.mockResolvedValueOnce({
          data: { id: `tool-${Date.now()}`, ...tool },
          error: null
        });
      }
      
      // Commit transaction
      await mockTransaction.commit();
      
      expect(mockTransaction.begin).toHaveBeenCalledTimes(1);
      expect(mockTransaction.commit).toHaveBeenCalledTimes(1);
      expect(mockTransaction.rollback).not.toHaveBeenCalled();
    });

    test('should rollback transaction on import failure', async () => {
      const importData = transactionTestData.partialFailureScenario;
      
      await mockTransaction.begin();
      
      try {
        // Simulate partial success then failure
        for (let i = 0; i < importData.length; i++) {
          if (i < 2) {
            // First 2 tools succeed
            mockSupabase.from().insert().select.mockResolvedValueOnce({
              data: { id: `tool-${i}`, ...importData[i] },
              error: null
            });
          } else {
            // Third tool fails
            mockSupabase.from().insert().select.mockResolvedValueOnce({
              data: null,
              error: new Error('Database constraint violation')
            });
            throw new Error('Import failed');
          }
        }
      } catch (error) {
        await mockTransaction.rollback();
      }
      
      expect(mockTransaction.begin).toHaveBeenCalledTimes(1);
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(1);
      expect(mockTransaction.commit).not.toHaveBeenCalled();
    });

    test('should handle nested transactions with savepoints', async () => {
      const importData = validTestTools.slice(0, 3);
      
      await mockTransaction.begin();
      
      // Create savepoint before each tool import
      for (let i = 0; i < importData.length; i++) {
        const savepointName = `tool_${i}`;
        await mockTransaction.savepoint(savepointName);
        
        try {
          // Simulate tool creation
          if (i === 1) {
            // Second tool fails
            throw new Error('Tool creation failed');
          }
          
          mockSupabase.from().insert().select.mockResolvedValueOnce({
            data: { id: `tool-${i}`, ...importData[i] },
            error: null
          });
          
          await mockTransaction.release(savepointName);
        } catch (error) {
          // Rollback to savepoint
          await mockTransaction.rollback(savepointName);
        }
      }
      
      await mockTransaction.commit();
      
      expect(mockTransaction.savepoint).toHaveBeenCalledTimes(3);
      expect(mockTransaction.release).toHaveBeenCalledTimes(2); // Only successful ones
      expect(mockTransaction.rollback).toHaveBeenCalledWith('tool_1'); // Failed one
    });
  });

  describe('Data Consistency', () => {
    test('should maintain referential integrity during import', async () => {
      const toolWithReferences = {
        name: 'Tool with References',
        link: '/tools/referenced-tool',
        category_id: 'ai-tools',
        company: 'Test Company',
        features: { ai: true, premium: false }
      };

      await mockTransaction.begin();

      // Simulate category lookup
      const categoryQuery = mockSupabase.from('categories');
      categoryQuery.select().eq().single.mockResolvedValueOnce({
        data: { id: 'ai-tools', name: 'AI Tools' },
        error: null
      });

      // Simulate tool creation
      const toolQuery = mockSupabase.from('tools');
      toolQuery.insert().select.mockResolvedValueOnce({
        data: { id: 'tool-1', ...toolWithReferences },
        error: null
      });

      await mockTransaction.commit();

      // Verify the operations were attempted
      expect(mockTransaction.begin).toHaveBeenCalled();
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    test('should handle foreign key constraint violations', async () => {
      const toolWithInvalidCategory = {
        name: 'Tool with Invalid Category',
        link: '/tools/invalid-category',
        category_id: 'non-existent-category'
      };
      
      await mockTransaction.begin();
      
      try {
        // Category lookup fails
        mockSupabase.from().select().eq().single.mockResolvedValueOnce({
          data: null,
          error: new Error('Category not found')
        });
        
        // Tool creation should fail due to invalid category
        mockSupabase.from().insert().select.mockResolvedValueOnce({
          data: null,
          error: new Error('Foreign key constraint violation')
        });
        
        throw new Error('Invalid category reference');
      } catch (error) {
        await mockTransaction.rollback();
      }
      
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(1);
    });

    test('should ensure unique constraints are respected', async () => {
      const duplicateTools = [
        { name: 'Unique Tool', link: '/tools/unique-tool' },
        { name: 'Unique Tool', link: '/tools/duplicate-name' } // Duplicate name
      ];
      
      await mockTransaction.begin();
      
      try {
        // First tool succeeds
        mockSupabase.from().insert().select.mockResolvedValueOnce({
          data: { id: 'tool-1', ...duplicateTools[0] },
          error: null
        });
        
        // Second tool fails due to unique constraint
        mockSupabase.from().insert().select.mockResolvedValueOnce({
          data: null,
          error: new Error('Unique constraint violation: name already exists')
        });
        
        throw new Error('Duplicate name detected');
      } catch (error) {
        await mockTransaction.rollback();
      }
      
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Recovery', () => {
    test('should recover from connection timeouts', async () => {
      const importData = validTestTools.slice(0, 2);
      let retryCount = 0;
      const maxRetries = 3;
      
      const attemptImport = async (): Promise<void> => {
        try {
          await mockTransaction.begin();
          
          if (retryCount < 2) {
            retryCount++;
            throw new Error('Connection timeout');
          }
          
          // Successful import on third attempt
          for (const tool of importData) {
            mockSupabase.from().insert().select.mockResolvedValueOnce({
              data: { id: `tool-${Date.now()}`, ...tool },
              error: null
            });
          }
          
          await mockTransaction.commit();
        } catch (error) {
          await mockTransaction.rollback();
          
          if (retryCount < maxRetries) {
            // Retry with exponential backoff
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
            return attemptImport();
          }
          
          throw error;
        }
      };
      
      await attemptImport();
      
      expect(retryCount).toBe(2);
      expect(mockTransaction.begin).toHaveBeenCalledTimes(3);
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(2);
      expect(mockTransaction.commit).toHaveBeenCalledTimes(1);
    });

    test('should handle deadlock detection and retry', async () => {
      const importData = validTestTools.slice(0, 1);
      let deadlockCount = 0;
      
      const handleDeadlock = async (): Promise<void> => {
        try {
          await mockTransaction.begin();
          
          if (deadlockCount === 0) {
            deadlockCount++;
            throw new Error('Deadlock detected');
          }
          
          // Successful import after deadlock resolution
          mockSupabase.from().insert().select.mockResolvedValueOnce({
            data: { id: 'tool-1', ...importData[0] },
            error: null
          });
          
          await mockTransaction.commit();
        } catch (error) {
          await mockTransaction.rollback();
          
          if (error.message.includes('Deadlock')) {
            // Random delay to avoid repeated deadlocks
            await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
            return handleDeadlock();
          }
          
          throw error;
        }
      };
      
      await handleDeadlock();
      
      expect(deadlockCount).toBe(1);
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(1);
      expect(mockTransaction.commit).toHaveBeenCalledTimes(1);
    });

    test('should cleanup resources on transaction failure', async () => {
      const importData = validTestTools.slice(0, 2);
      const resources: string[] = [];
      
      try {
        await mockTransaction.begin();
        
        // Allocate resources
        resources.push('temp_file_1', 'temp_file_2');
        
        // Simulate failure during import
        throw new Error('Import process failed');
      } catch (error) {
        await mockTransaction.rollback();
        
        // Cleanup resources
        resources.forEach(resource => {
          // Simulate resource cleanup
          expect(resource).toBeDefined();
        });
        resources.length = 0; // Clear array
      }
      
      expect(resources).toHaveLength(0);
      expect(mockTransaction.rollback).toHaveBeenCalledTimes(1);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large batch imports efficiently', async () => {
      const largeBatch = Array.from({ length: 1000 }, (_, i) => ({
        name: `Tool ${i}`,
        link: `/tools/tool-${i}`,
        description: `Description for tool ${i}`
      }));
      
      const batchSize = 100;
      const batches = [];
      
      // Split into smaller batches
      for (let i = 0; i < largeBatch.length; i += batchSize) {
        batches.push(largeBatch.slice(i, i + batchSize));
      }
      
      await mockTransaction.begin();
      
      // Process each batch
      for (const batch of batches) {
        const savepointName = `batch_${batches.indexOf(batch)}`;
        await mockTransaction.savepoint(savepointName);
        
        try {
          // Mock batch insert
          mockSupabase.from().insert().select.mockResolvedValueOnce({
            data: batch.map((tool, i) => ({ id: `tool-${i}`, ...tool })),
            error: null
          });
          
          await mockTransaction.release(savepointName);
        } catch (error) {
          await mockTransaction.rollback(savepointName);
        }
      }
      
      await mockTransaction.commit();
      
      expect(batches).toHaveLength(10); // 1000 / 100
      expect(mockTransaction.savepoint).toHaveBeenCalledTimes(10);
      expect(mockTransaction.commit).toHaveBeenCalledTimes(1);
    });

    test('should monitor transaction duration and timeout', async () => {
      const importData = validTestTools.slice(0, 1);
      const transactionTimeout = 30000; // 30 seconds
      const startTime = Date.now();
      
      await mockTransaction.begin();
      
      // Simulate long-running operation
      const processWithTimeout = async (): Promise<void> => {
        return new Promise((resolve, reject) => {
          const timer = setTimeout(() => {
            reject(new Error('Transaction timeout'));
          }, transactionTimeout);
          
          // Simulate quick completion
          setTimeout(() => {
            clearTimeout(timer);
            resolve();
          }, 100);
        });
      };
      
      try {
        await processWithTimeout();
        
        mockSupabase.from().insert().select.mockResolvedValueOnce({
          data: { id: 'tool-1', ...importData[0] },
          error: null
        });
        
        await mockTransaction.commit();
      } catch (error) {
        await mockTransaction.rollback();
        throw error;
      }
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(transactionTimeout);
      expect(mockTransaction.commit).toHaveBeenCalledTimes(1);
    });
  });
});
